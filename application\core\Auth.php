<?php
/**
 * Authentication Class
 * Handles user authentication and authorization
 */

class Auth {
    private static $loginAttempts = [];
    
    /**
     * Attempt to log in user
     */
    public static function attempt($username, $password) {
        // Check if user is locked out
        if (self::isLockedOut($username)) {
            return [
                'success' => false,
                'message' => 'Account temporarily locked due to too many failed attempts. Try again later.'
            ];
        }
        
        $userModel = new User();
        $user = $userModel->findByUsername($username);
        
        if (!$user || !password_verify($password, $user['password_hash'])) {
            self::recordFailedAttempt($username);
            return [
                'success' => false,
                'message' => 'Invalid username or password.'
            ];
        }
        
        // Clear failed attempts on successful login
        self::clearFailedAttempts($username);
        
        // Set session data
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        
        // Log successful login
        logActivity('User Login', "User {$username} logged in successfully");
        
        return [
            'success' => true,
            'message' => 'Login successful.',
            'user' => $user
        ];
    }
    
    /**
     * Log out user
     */
    public static function logout() {
        $username = $_SESSION['username'] ?? 'Unknown';
        
        // Log logout activity
        logActivity('User Logout', "User {$username} logged out");
        
        // Destroy session
        session_destroy();
        
        // Start new session for flash messages
        session_start();
        $_SESSION['flash']['success'] = 'You have been logged out successfully.';
    }
    
    /**
     * Check if user is logged in
     */
    public static function isLoggedIn() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
    
    /**
     * Check if user is admin
     */
    public static function isAdmin() {
        return self::isLoggedIn() && $_SESSION['user_role'] === 'admin';
    }
    
    /**
     * Check if user is staff
     */
    public static function isStaff() {
        return self::isLoggedIn() && $_SESSION['user_role'] === 'staff';
    }
    
    /**
     * Get current user ID
     */
    public static function getUserId() {
        return $_SESSION['user_id'] ?? null;
    }
    
    /**
     * Get current username
     */
    public static function getUsername() {
        return $_SESSION['username'] ?? null;
    }
    
    /**
     * Get current user role
     */
    public static function getUserRole() {
        return $_SESSION['user_role'] ?? null;
    }
    
    /**
     * Record failed login attempt
     */
    private static function recordFailedAttempt($username) {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $key = $username . '_' . $ip;
        
        if (!isset(self::$loginAttempts[$key])) {
            self::$loginAttempts[$key] = [
                'count' => 0,
                'last_attempt' => time()
            ];
        }
        
        self::$loginAttempts[$key]['count']++;
        self::$loginAttempts[$key]['last_attempt'] = time();
        
        // Store in session for persistence
        $_SESSION['login_attempts'] = self::$loginAttempts;
        
        // Log failed attempt
        logActivity('Failed Login Attempt', "Failed login attempt for username: {$username} from IP: {$ip}");
    }
    
    /**
     * Clear failed login attempts
     */
    private static function clearFailedAttempts($username) {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $key = $username . '_' . $ip;
        
        unset(self::$loginAttempts[$key]);
        $_SESSION['login_attempts'] = self::$loginAttempts;
    }
    
    /**
     * Check if user is locked out
     */
    private static function isLockedOut($username) {
        // Load attempts from session
        self::$loginAttempts = $_SESSION['login_attempts'] ?? [];
        
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $key = $username . '_' . $ip;
        
        if (!isset(self::$loginAttempts[$key])) {
            return false;
        }
        
        $attempts = self::$loginAttempts[$key];
        
        // Check if lockout time has passed
        if (time() - $attempts['last_attempt'] > LOGIN_LOCKOUT_TIME) {
            self::clearFailedAttempts($username);
            return false;
        }
        
        return $attempts['count'] >= MAX_LOGIN_ATTEMPTS;
    }
    
    /**
     * Hash password
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Verify password
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Generate secure random token
     */
    public static function generateToken($length = 32) {
        return bin2hex(random_bytes($length));
    }
    
    /**
     * Check password strength
     */
    public static function checkPasswordStrength($password) {
        $errors = [];
        
        if (strlen($password) < PASSWORD_MIN_LENGTH) {
            $errors[] = "Password must be at least " . PASSWORD_MIN_LENGTH . " characters long";
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = "Password must contain at least one uppercase letter";
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = "Password must contain at least one lowercase letter";
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = "Password must contain at least one number";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Require authentication
     */
    public static function requireAuth() {
        if (!self::isLoggedIn()) {
            redirectTo('login');
        }
    }
    
    /**
     * Require admin role
     */
    public static function requireAdmin() {
        self::requireAuth();
        if (!self::isAdmin()) {
            header("HTTP/1.0 403 Forbidden");
            echo "Access denied. Admin privileges required.";
            exit();
        }
    }
    
    /**
     * Get remaining lockout time
     */
    public static function getRemainingLockoutTime($username) {
        self::$loginAttempts = $_SESSION['login_attempts'] ?? [];
        
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $key = $username . '_' . $ip;
        
        if (!isset(self::$loginAttempts[$key])) {
            return 0;
        }
        
        $attempts = self::$loginAttempts[$key];
        $elapsed = time() - $attempts['last_attempt'];
        $remaining = LOGIN_LOCKOUT_TIME - $elapsed;
        
        return max(0, $remaining);
    }
}
