<?php
/**
 * Home Controller
 * Handles the root route and redirects appropriately
 */

class HomeController extends Controller {
    
    public function __construct() {
        // Don't call parent constructor to avoid session timeout check for unauthenticated users
        $this->view = new View();
        $this->cache = new Cache();
    }
    
    /**
     * Handle root route
     */
    public function index() {
        // Check if user is logged in
        if (Auth::isLoggedIn()) {
            // Redirect to dashboard if logged in
            redirectTo('dashboard');
        } else {
            // Redirect to login if not logged in
            redirectTo('login');
        }
    }
}
