<?php
/**
 * Router Class
 * Handles URL routing and middleware
 */

class Router {
    private $routes = [];
    private $middleware = [];
    
    public function __construct() {
        $this->defineRoutes();
    }
    
    /**
     * Define all application routes
     */
    private function defineRoutes() {
        // Public routes (no authentication required)
        $this->routes = [
            '' => 'HomeController@index',
            'login' => 'LoginController@display',
            'login/attempt' => 'LoginController@attempt',
            'logout' => 'LoginController@logout',
            
            // Protected routes (authentication required)
            'dashboard' => 'DashboardController@index',
            'dashboard/clear-cache' => 'DashboardController@clearCache',
            
            // Invoice routes
            'invoices' => 'InvoiceController@index',
            'invoices/create' => 'InvoiceController@create',
            'invoices/store' => 'InvoiceController@store',
            'invoices/edit' => 'InvoiceController@edit',
            'invoices/update' => 'InvoiceController@update',
            'invoices/delete' => 'InvoiceController@delete',
            'invoices/view' => 'InvoiceController@view',
            'invoices/pdf' => 'InvoiceController@generatePDF',
            
            // Stock routes
            'stock' => 'StockController@index',
            'stock/create' => 'StockController@create',
            'stock/store' => 'StockController@store',
            'stock/edit' => 'StockController@edit',
            'stock/update' => 'StockController@update',
            'stock/delete' => 'StockController@delete',
            'stock/low-stock' => 'StockController@lowStock',
            
            // Customer routes
            'customers' => 'CustomerController@index',
            'customers/create' => 'CustomerController@create',
            'customers/store' => 'CustomerController@store',
            'customers/edit' => 'CustomerController@edit',
            'customers/update' => 'CustomerController@update',
            'customers/delete' => 'CustomerController@delete',
            
            // User management routes (admin only)
            'users' => 'UserController@index',
            'users/create' => 'UserController@create',
            'users/store' => 'UserController@store',
            'users/edit' => 'UserController@edit',
            'users/update' => 'UserController@update',
            'users/delete' => 'UserController@delete',
            
            // Settings routes (admin only)
            'settings' => 'SettingsController@index',
            'settings/update' => 'SettingsController@update',
            
            // Reports routes
            'reports' => 'ReportsController@index',
            'reports/sales' => 'ReportsController@sales',
            'reports/stock' => 'ReportsController@stock',
            'reports/export' => 'ReportsController@export',
            
            // API routes
            'api/stock/search' => 'ApiController@searchStock',
            'api/customers/search' => 'ApiController@searchCustomers',
            'api/dashboard/stats' => 'ApiController@dashboardStats',
        ];
        
        // Define middleware for protected routes
        $this->middleware = [
            'auth' => [
                'dashboard', 'dashboard/clear-cache', 'invoices', 'stock', 'customers', 'reports',
                'invoices/create', 'invoices/store', 'invoices/edit', 'invoices/update', 'invoices/delete', 'invoices/view', 'invoices/pdf',
                'stock/create', 'stock/store', 'stock/edit', 'stock/update', 'stock/delete', 'stock/low-stock',
                'customers/create', 'customers/store', 'customers/edit', 'customers/update', 'customers/delete',
                'reports/sales', 'reports/stock', 'reports/export',
                'api/stock/search', 'api/customers/search', 'api/dashboard/stats'
            ],
            'admin' => [
                'users', 'users/create', 'users/store', 'users/edit', 'users/update', 'users/delete',
                'settings', 'settings/update', 'dashboard/clear-cache'
            ]
        ];
    }
    
    /**
     * Handle incoming request
     */
    public function handleRequest() {
        $uri = $this->getUri();
        
        // Check if route exists
        if (!isset($this->routes[$uri])) {
            $this->handle404();
            return;
        }
        
        // Apply middleware
        if (!$this->applyMiddleware($uri)) {
            return;
        }
        
        // Execute route
        $this->executeRoute($this->routes[$uri]);
    }
    
    /**
     * Get clean URI from request
     */
    private function getUri() {
        $uri = $_GET['page'] ?? '';
        
        // Handle additional parameters
        if (isset($_GET['id'])) {
            $uri .= '/' . $_GET['id'];
        }
        
        return trim($uri, '/');
    }
    
    /**
     * Apply middleware to route
     */
    private function applyMiddleware($uri) {
        // Check authentication middleware
        if (in_array($uri, $this->middleware['auth'])) {
            if (!Auth::isLoggedIn()) {
                redirectTo('login');
                return false;
            }
        }
        
        // Check admin middleware
        if (in_array($uri, $this->middleware['admin'])) {
            if (!Auth::isAdmin()) {
                $this->handle403();
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Execute route controller and method
     */
    private function executeRoute($route) {
        list($controller, $method) = explode('@', $route);
        
        if (class_exists($controller)) {
            $instance = new $controller();
            if (method_exists($instance, $method)) {
                $instance->$method();
            } else {
                $this->handle404();
            }
        } else {
            $this->handle404();
        }
    }
    
    /**
     * Handle 404 Not Found
     */
    private function handle404() {
        header("HTTP/1.0 404 Not Found");
        $view = new View();
        $view->render('errors/404', [
            'title' => '404 - Page Not Found',
            'message' => 'The requested page could not be found.'
        ]);
    }
    
    /**
     * Handle 403 Forbidden
     */
    private function handle403() {
        header("HTTP/1.0 403 Forbidden");
        $view = new View();
        $view->render('errors/403', [
            'title' => '403 - Access Forbidden',
            'message' => 'You do not have permission to access this resource.'
        ]);
    }
    
    /**
     * Add custom route
     */
    public function addRoute($uri, $controller) {
        $this->routes[$uri] = $controller;
    }
    
    /**
     * Add middleware to route
     */
    public function addMiddleware($type, $routes) {
        if (!isset($this->middleware[$type])) {
            $this->middleware[$type] = [];
        }
        $this->middleware[$type] = array_merge($this->middleware[$type], (array)$routes);
    }
}
