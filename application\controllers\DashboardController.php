<?php
/**
 * Dashboard Controller
 * Handles main dashboard functionality
 */

class DashboardController {
    private $view;
    private $cache;

    public function __construct() {
        $this->view = new View();
        $this->cache = new Cache();
    }
    
    /**
     * Display dashboard
     */
    public function index() {
        $data = [
            'title' => 'Dashboard - ' . APP_NAME,
            'stats' => $this->getDashboardStats(),
            'recent_invoices' => $this->getRecentInvoices(),
            'low_stock_products' => $this->getLowStockProducts(),
            'overdue_invoices' => $this->getOverdueInvoices(),
            'monthly_sales' => $this->getMonthlySales(),
            'top_customers' => $this->getTopCustomers()
        ];

        $this->logActivity('Dashboard View', 'Accessed dashboard');
        $this->render('dashboard/index', $data, 'main');
    }
    
    /**
     * Get dashboard statistics
     */
    private function getDashboardStats() {
        $cacheKey = 'dashboard_stats_' . date('Y-m-d-H');
        
        return $this->cache->remember($cacheKey, function() {
            try {
                $invoiceModel = new Invoice();
                $stockModel = new Stock();
                $customerModel = new Customer();
                $userModel = new User();

                $invoiceStats = $invoiceModel->getStats() ?: [];
                $stockStats = $stockModel->getStats() ?: [];
                $customerStats = $customerModel->getStats() ?: [];
                $userStats = $userModel->getStats() ?: [];

                return [
                    // Invoice Statistics
                    'total_invoices' => $invoiceStats['total_invoices'] ?? 0,
                    'draft_invoices' => $invoiceStats['draft_invoices'] ?? 0,
                    'paid_invoices' => $invoiceStats['paid_invoices'] ?? 0,
                    'overdue_invoices' => $invoiceStats['overdue_invoices'] ?? 0,
                    'total_revenue' => $invoiceStats['total_revenue'] ?? 0,
                    'pending_amount' => $invoiceStats['pending_amount'] ?? 0,
                    'monthly_revenue' => $invoiceStats['monthly_revenue'] ?? 0,

                    // Stock Statistics
                    'total_products' => $stockStats['total_products'] ?? 0,
                    'low_stock_products' => $stockStats['low_stock_products'] ?? 0,
                    'out_of_stock_products' => $stockStats['out_of_stock_products'] ?? 0,
                    'total_stock_value' => $stockStats['total_stock_value'] ?? 0,

                    // Customer Statistics
                    'total_customers' => $customerStats['total_customers'] ?? 0,
                    'new_customers_this_month' => $customerStats['new_customers_this_month'] ?? 0,
                    'customers_with_outstanding' => $customerStats['customers_with_outstanding'] ?? 0,
                    'total_outstanding' => $customerStats['total_outstanding'] ?? 0,

                    // User Statistics
                    'total_users' => $userStats['total_users'] ?? 0,
                    'admin_count' => $userStats['admin_count'] ?? 0,
                    'staff_count' => $userStats['staff_count'] ?? 0
                ];
            } catch (Exception $e) {
                // Return default values if there's an error
                return [
                    'total_invoices' => 0, 'draft_invoices' => 0, 'paid_invoices' => 0, 'overdue_invoices' => 0,
                    'total_revenue' => 0, 'pending_amount' => 0, 'monthly_revenue' => 0,
                    'total_products' => 0, 'low_stock_products' => 0, 'out_of_stock_products' => 0, 'total_stock_value' => 0,
                    'total_customers' => 0, 'new_customers_this_month' => 0, 'customers_with_outstanding' => 0, 'total_outstanding' => 0,
                    'total_users' => 0, 'admin_count' => 0, 'staff_count' => 0
                ];
            }
        }, 3600); // Cache for 1 hour
    }
    
    /**
     * Get recent invoices
     */
    private function getRecentInvoices() {
        $cacheKey = 'recent_invoices_' . date('Y-m-d-H');
        
        return $this->cache->remember($cacheKey, function() {
            try {
                $invoiceModel = new Invoice();
                return $invoiceModel->getAll(1, 5) ?: []; // Get 5 recent invoices
            } catch (Exception $e) {
                return [];
            }
        }, 1800); // Cache for 30 minutes
    }
    
    /**
     * Get low stock products
     */
    private function getLowStockProducts() {
        $cacheKey = 'low_stock_products_' . date('Y-m-d-H');
        
        return $this->cache->remember($cacheKey, function() {
            try {
                $stockModel = new Stock();
                $lowStock = $stockModel->getLowStockProducts() ?: [];
                return array_slice($lowStock, 0, 5); // Get 5 low stock products
            } catch (Exception $e) {
                return [];
            }
        }, 1800); // Cache for 30 minutes
    }
    
    /**
     * Get overdue invoices
     */
    private function getOverdueInvoices() {
        $cacheKey = 'overdue_invoices_' . date('Y-m-d');
        
        return $this->cache->remember($cacheKey, function() {
            try {
                $invoiceModel = new Invoice();
                $overdue = $invoiceModel->getOverdueInvoices() ?: [];
                return array_slice($overdue, 0, 5); // Get 5 overdue invoices
            } catch (Exception $e) {
                return [];
            }
        }, 3600); // Cache for 1 hour
    }
    
    /**
     * Get monthly sales data for chart
     */
    private function getMonthlySales() {
        $cacheKey = 'monthly_sales_' . date('Y-m');
        
        return $this->cache->remember($cacheKey, function() {
            $invoiceModel = new Invoice();
            
            // Get sales data for last 12 months
            $sql = "SELECT 
                        DATE_FORMAT(invoice_date, '%Y-%m') as month,
                        SUM(total_amount) as total_sales,
                        COUNT(*) as invoice_count
                    FROM invoices 
                    WHERE invoice_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                    AND status = 'paid'
                    GROUP BY DATE_FORMAT(invoice_date, '%Y-%m')
                    ORDER BY month ASC";
            
            $result = $invoiceModel->query($sql);
            return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
        }, 3600); // Cache for 1 hour
    }
    
    /**
     * Get top customers
     */
    private function getTopCustomers() {
        $cacheKey = 'top_customers_' . date('Y-m');
        
        return $this->cache->remember($cacheKey, function() {
            try {
                $customerModel = new Customer();
                return $customerModel->getTopCustomers(5) ?: []; // Get top 5 customers
            } catch (Exception $e) {
                return [];
            }
        }, 3600); // Cache for 1 hour
    }
    
    /**
     * Get dashboard data for AJAX requests
     */
    public function getStats() {
        if (!$this->isAjax()) {
            redirectTo('dashboard');
        }
        
        $stats = $this->getDashboardStats();
        $this->renderJson(['success' => true, 'data' => $stats]);
    }
    
    /**
     * Get recent activities
     */
    public function getRecentActivities() {
        if (!$this->isAjax()) {
            redirectTo('dashboard');
        }
        
        $cacheKey = 'recent_activities_' . date('Y-m-d-H');
        
        $activities = $this->cache->remember($cacheKey, function() {
            $sql = "SELECT al.*, u.username
                    FROM activity_logs al
                    LEFT JOIN users u ON al.user_id = u.id
                    ORDER BY al.created_at DESC
                    LIMIT 10";

            $userModel = new User();
            $result = $userModel->query($sql);
            return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
        }, 900); // Cache for 15 minutes
        
        $this->renderJson(['success' => true, 'data' => $activities]);
    }
    
    /**
     * Get sales chart data
     */
    public function getSalesChart() {
        if (!$this->isAjax()) {
            redirectTo('dashboard');
        }
        
        $period = $_GET['period'] ?? 'monthly';
        $cacheKey = "sales_chart_{$period}_" . date('Y-m-d');
        
        $chartData = $this->cache->remember($cacheKey, function() use ($period) {
            $invoiceModel = new Invoice();
            
            switch ($period) {
                case 'daily':
                    $sql = "SELECT 
                                DATE(invoice_date) as period,
                                SUM(total_amount) as total_sales,
                                COUNT(*) as invoice_count
                            FROM invoices 
                            WHERE invoice_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                            AND status = 'paid'
                            GROUP BY DATE(invoice_date)
                            ORDER BY period ASC";
                    break;
                    
                case 'weekly':
                    $sql = "SELECT 
                                YEARWEEK(invoice_date) as period,
                                SUM(total_amount) as total_sales,
                                COUNT(*) as invoice_count
                            FROM invoices 
                            WHERE invoice_date >= DATE_SUB(CURDATE(), INTERVAL 12 WEEK)
                            AND status = 'paid'
                            GROUP BY YEARWEEK(invoice_date)
                            ORDER BY period ASC";
                    break;
                    
                default: // monthly
                    $sql = "SELECT 
                                DATE_FORMAT(invoice_date, '%Y-%m') as period,
                                SUM(total_amount) as total_sales,
                                COUNT(*) as invoice_count
                            FROM invoices 
                            WHERE invoice_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                            AND status = 'paid'
                            GROUP BY DATE_FORMAT(invoice_date, '%Y-%m')
                            ORDER BY period ASC";
                    break;
            }
            
            $result = $invoiceModel->query($sql);
            return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
        }, 3600); // Cache for 1 hour
        
        $this->renderJson(['success' => true, 'data' => $chartData]);
    }
    
    /**
     * Get product performance data
     */
    public function getProductPerformance() {
        if (!$this->isAjax()) {
            redirectTo('dashboard');
        }
        
        $cacheKey = 'product_performance_' . date('Y-m');
        
        $productData = $this->cache->remember($cacheKey, function() {
            $sql = "SELECT 
                        p.name as product_name,
                        SUM(ii.quantity) as total_sold,
                        SUM(ii.line_total) as total_revenue
                    FROM invoice_items ii
                    INNER JOIN products p ON ii.product_id = p.id
                    INNER JOIN invoices i ON ii.invoice_id = i.id
                    WHERE i.status = 'paid'
                    AND i.invoice_date >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
                    GROUP BY p.id, p.name
                    ORDER BY total_revenue DESC
                    LIMIT 10";
            
            $invoiceModel = new Invoice();
            $result = $invoiceModel->query($sql);
            return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
        }, 3600); // Cache for 1 hour
        
        $this->renderJson(['success' => true, 'data' => $productData]);
    }
    
    /**
     * Clear dashboard cache
     */
    public function clearCache() {
        if (!Auth::isAdmin()) {
            $this->renderJson(['success' => false, 'message' => 'Access denied'], 403);
            return;
        }
        
        $patterns = [
            'dashboard_stats_',
            'recent_invoices_',
            'low_stock_products_',
            'overdue_invoices_',
            'monthly_sales_',
            'top_customers_',
            'recent_activities_',
            'sales_chart_',
            'product_performance_'
        ];
        
        $cleared = 0;
        foreach ($patterns as $pattern) {
            $cleared += $this->cache->invalidatePattern($pattern);
        }
        
        $this->logActivity('Cache Clear', "Cleared dashboard cache ($cleared items)");
        $this->renderJson(['success' => true, 'message' => "Cache cleared ($cleared items)"]);
    }

    /**
     * Log user activity
     */
    private function logActivity($action, $details = '') {
        logActivity($action, $details);
    }

    /**
     * Render JSON response
     */
    private function renderJson($data, $status = 200) {
        header('Content-Type: application/json');
        http_response_code($status);
        echo json_encode($data);
        exit();
    }


}
