<?php
/**
 * Cache Class
 * File-based caching system for improved performance
 */

class Cache {
    private $cacheDir;
    private $defaultLifetime;
    
    public function __construct() {
        $this->cacheDir = CACHE_PATH;
        $this->defaultLifetime = CACHE_LIFETIME;
        
        // Create cache directory if it doesn't exist
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * Store data in cache
     */
    public function set($key, $data, $lifetime = null) {
        if (!CACHE_ENABLED) {
            return false;
        }
        
        $lifetime = $lifetime ?? $this->defaultLifetime;
        $filename = $this->getFilename($key);
        
        $cacheData = [
            'data' => $data,
            'expires' => time() + $lifetime,
            'created' => time()
        ];
        
        return file_put_contents($filename, serialize($cacheData), LOCK_EX) !== false;
    }
    
    /**
     * Retrieve data from cache
     */
    public function get($key, $default = null) {
        if (!CACHE_ENABLED) {
            return $default;
        }
        
        $filename = $this->getFilename($key);
        
        if (!file_exists($filename)) {
            return $default;
        }
        
        $content = file_get_contents($filename);
        if ($content === false) {
            return $default;
        }
        
        $cacheData = unserialize($content);
        if ($cacheData === false) {
            // Invalid cache data, delete file
            unlink($filename);
            return $default;
        }
        
        // Check if cache has expired
        if (time() > $cacheData['expires']) {
            unlink($filename);
            return $default;
        }
        
        return $cacheData['data'];
    }
    
    /**
     * Check if cache key exists and is valid
     */
    public function has($key) {
        if (!CACHE_ENABLED) {
            return false;
        }
        
        $filename = $this->getFilename($key);
        
        if (!file_exists($filename)) {
            return false;
        }
        
        $content = file_get_contents($filename);
        if ($content === false) {
            return false;
        }
        
        $cacheData = unserialize($content);
        if ($cacheData === false) {
            unlink($filename);
            return false;
        }
        
        // Check if cache has expired
        if (time() > $cacheData['expires']) {
            unlink($filename);
            return false;
        }
        
        return true;
    }
    
    /**
     * Delete cache entry
     */
    public function delete($key) {
        $filename = $this->getFilename($key);
        
        if (file_exists($filename)) {
            return unlink($filename);
        }
        
        return true;
    }
    
    /**
     * Clear all cache
     */
    public function clear() {
        $files = glob($this->cacheDir . '*.cache');
        $deleted = 0;
        
        foreach ($files as $file) {
            if (unlink($file)) {
                $deleted++;
            }
        }
        
        return $deleted;
    }
    
    /**
     * Clear expired cache entries
     */
    public function clearExpired() {
        $files = glob($this->cacheDir . '*.cache');
        $deleted = 0;
        
        foreach ($files as $file) {
            $content = file_get_contents($file);
            if ($content === false) {
                continue;
            }
            
            $cacheData = unserialize($content);
            if ($cacheData === false || time() > $cacheData['expires']) {
                if (unlink($file)) {
                    $deleted++;
                }
            }
        }
        
        return $deleted;
    }
    
    /**
     * Get cache statistics
     */
    public function getStats() {
        $files = glob($this->cacheDir . '*.cache');
        $totalSize = 0;
        $validEntries = 0;
        $expiredEntries = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            
            $content = file_get_contents($file);
            if ($content === false) {
                continue;
            }
            
            $cacheData = unserialize($content);
            if ($cacheData === false) {
                $expiredEntries++;
                continue;
            }
            
            if (time() > $cacheData['expires']) {
                $expiredEntries++;
            } else {
                $validEntries++;
            }
        }
        
        return [
            'total_entries' => count($files),
            'valid_entries' => $validEntries,
            'expired_entries' => $expiredEntries,
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize)
        ];
    }
    
    /**
     * Remember data with cache
     */
    public function remember($key, $callback, $lifetime = null) {
        $data = $this->get($key);
        
        if ($data === null) {
            $data = $callback();
            $this->set($key, $data, $lifetime);
        }
        
        return $data;
    }
    
    /**
     * Get cache filename for key
     */
    private function getFilename($key) {
        $hash = md5($key);
        return $this->cacheDir . $hash . '.cache';
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Cache database query results
     */
    public function cacheQuery($sql, $params, $callback, $lifetime = null) {
        $key = 'query_' . md5($sql . serialize($params));
        return $this->remember($key, $callback, $lifetime);
    }
    
    /**
     * Invalidate cache by pattern
     */
    public function invalidatePattern($pattern) {
        $files = glob($this->cacheDir . '*.cache');
        $deleted = 0;
        
        foreach ($files as $file) {
            $content = file_get_contents($file);
            if ($content === false) {
                continue;
            }
            
            $cacheData = unserialize($content);
            if ($cacheData === false) {
                continue;
            }
            
            // This is a simple pattern matching - could be enhanced
            if (strpos($file, $pattern) !== false) {
                if (unlink($file)) {
                    $deleted++;
                }
            }
        }
        
        return $deleted;
    }
}
