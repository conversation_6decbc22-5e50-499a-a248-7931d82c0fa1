<?php
/**
 * Simple Test File
 * Basic functionality test without complex routing
 */

// Start session
session_start();

// Set Indian timezone
date_default_timezone_set('Asia/Kolkata');

// Include configuration
require_once 'config/config.php';

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Simple Test - " . APP_NAME . "</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";

echo "<div class='card'>";
echo "<div class='card-header'>";
echo "<h3>Invoice & Stock Management System - Simple Test</h3>";
echo "</div>";
echo "<div class='card-body'>";

// Test configuration
echo "<h5>Configuration Test</h5>";
echo "<ul class='list-group mb-3'>";
echo "<li class='list-group-item'>App Name: " . APP_NAME . "</li>";
echo "<li class='list-group-item'>Environment: " . ENVIRONMENT . "</li>";
echo "<li class='list-group-item'>Base URL: " . BASE_URL . "</li>";
echo "<li class='list-group-item'>Database: " . DB_NAME . "</li>";
echo "<li class='list-group-item'>Timezone: " . TIMEZONE . "</li>";
echo "<li class='list-group-item'>Currency: " . CURRENCY_SYMBOL . "</li>";
echo "</ul>";

// Test database connection
echo "<h5>Database Connection Test</h5>";
try {
    $db = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($db->connect_errno) {
        echo "<div class='alert alert-danger'>Database connection failed: " . $db->connect_error . "</div>";
    } else {
        echo "<div class='alert alert-success'>Database connection successful!</div>";
        
        // Test if tables exist
        $tables = ['users', 'invoices', 'products', 'customers'];
        echo "<h6>Table Check:</h6>";
        echo "<ul class='list-group mb-3'>";
        foreach ($tables as $table) {
            $result = $db->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                echo "<li class='list-group-item list-group-item-success'>✓ Table '$table' exists</li>";
            } else {
                echo "<li class='list-group-item list-group-item-warning'>⚠ Table '$table' missing</li>";
            }
        }
        echo "</ul>";
        
        $db->close();
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Database error: " . $e->getMessage() . "</div>";
}

// Test core files
echo "<h5>Core Files Test</h5>";
$core_files = [
    'application/core/Model.php',
    'application/core/View.php', 
    'application/core/Controller.php',
    'application/core/Router.php',
    'application/core/Auth.php'
];

echo "<ul class='list-group mb-3'>";
foreach ($core_files as $file) {
    if (file_exists($file)) {
        echo "<li class='list-group-item list-group-item-success'>✓ $file exists</li>";
    } else {
        echo "<li class='list-group-item list-group-item-danger'>✗ $file missing</li>";
    }
}
echo "</ul>";

// Simple login form
echo "<h5>Quick Login Test</h5>";
echo "<form method='POST' action='simple_test.php'>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<input type='text' class='form-control' name='username' placeholder='Username' value='admin'>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<input type='password' class='form-control' name='password' placeholder='Password' value='admin123'>";
echo "</div>";
echo "</div>";
echo "<button type='submit' class='btn btn-primary mt-2'>Test Login</button>";
echo "</form>";

// Handle login test
if ($_POST) {
    echo "<div class='mt-3'>";
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if ($username && $password) {
        try {
            $db = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
            $stmt = $db->prepare("SELECT * FROM users WHERE username = ?");
            $stmt->bind_param('s', $username);
            $stmt->execute();
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();
            
            if ($user && password_verify($password, $user['password_hash'])) {
                echo "<div class='alert alert-success'>✓ Login successful! User: " . $user['username'] . " (Role: " . $user['role'] . ")</div>";
            } else {
                echo "<div class='alert alert-danger'>✗ Invalid credentials</div>";
            }
            
            $db->close();
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>Login test error: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";
}

echo "<div class='mt-4'>";
echo "<h5>Next Steps</h5>";
echo "<div class='d-grid gap-2'>";
echo "<a href='test_setup.php' class='btn btn-info'>Run Full Setup Test</a>";
echo "<a href='index.php' class='btn btn-success'>Go to Main Application</a>";
echo "<a href='phpinfo.php' class='btn btn-secondary'>PHP Info</a>";
echo "</div>";
echo "</div>";

echo "</div>"; // card-body
echo "</div>"; // card
echo "</div>"; // col
echo "</div>"; // row
echo "</div>"; // container

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
