<?php
class User extends Model {
    public function __construct() {
        parent::__construct();
    }

    public function findByUsername($username) {
        $sql = "SELECT * FROM users WHERE username = ?";
        $result = $this->query($sql, [$username]);
        return $result->fetch_assoc();
    }

    public function verifyPassword($username, $password) {
        $user = $this->findByUsername($username);
        if ($user) {
            return password_verify($password, $user['password_hash']);
        }
        return false;
    }

    public function getRole($username) {
        $sql = "SELECT role FROM users WHERE username = ?";
        $result = $this->query($sql, [$username]);
        return $result->fetch_assoc()['role'];
    }
}