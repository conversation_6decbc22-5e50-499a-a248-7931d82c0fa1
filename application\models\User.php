<?php
/**
 * User Model
 * Handles user-related database operations
 */

class User extends Model {
    protected $table = 'users';

    public function __construct() {
        parent::__construct();
    }

    /**
     * Find user by username
     */
    public function findByUsername($username) {
        $sql = "SELECT * FROM users WHERE username = ? AND is_active = 1";
        $result = $this->query($sql, [$username]);
        return $result ? $result->fetch_assoc() : null;
    }

    /**
     * Find user by email
     */
    public function findByEmail($email) {
        $sql = "SELECT * FROM users WHERE email = ? AND is_active = 1";
        $result = $this->query($sql, [$email]);
        return $result ? $result->fetch_assoc() : null;
    }

    /**
     * Find user by ID
     */
    public function findById($id) {
        $sql = "SELECT * FROM users WHERE id = ? AND is_active = 1";
        $result = $this->query($sql, [$id]);
        return $result ? $result->fetch_assoc() : null;
    }

    /**
     * Get all users with pagination
     */
    public function getAll($page = 1, $limit = RECORDS_PER_PAGE, $search = '') {
        $offset = ($page - 1) * $limit;

        $whereClause = "WHERE is_active = 1";
        $params = [];

        if (!empty($search)) {
            $whereClause .= " AND (username LIKE ? OR email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)";
            $searchTerm = "%$search%";
            $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
        }

        $sql = "SELECT * FROM users $whereClause ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;

        $result = $this->query($sql, $params);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }

    /**
     * Get total user count
     */
    public function getTotalCount($search = '') {
        $whereClause = "WHERE is_active = 1";
        $params = [];

        if (!empty($search)) {
            $whereClause .= " AND (username LIKE ? OR email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)";
            $searchTerm = "%$search%";
            $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
        }

        $sql = "SELECT COUNT(*) as total FROM users $whereClause";
        $result = $this->query($sql, $params);
        return $result ? $result->fetch_assoc()['total'] : 0;
    }

    /**
     * Create new user
     */
    public function create($data) {
        $sql = "INSERT INTO users (username, email, password_hash, role, first_name, last_name, phone)
                VALUES (?, ?, ?, ?, ?, ?, ?)";

        $params = [
            $data['username'],
            $data['email'] ?? null,
            password_hash($data['password'], PASSWORD_DEFAULT),
            $data['role'],
            $data['first_name'] ?? null,
            $data['last_name'] ?? null,
            $data['phone'] ?? null
        ];

        $result = $this->query($sql, $params);
        return $result ? $this->db->insert_id : false;
    }

    /**
     * Update user
     */
    public function update($id, $data) {
        $setParts = [];
        $params = [];

        $allowedFields = ['username', 'email', 'role', 'first_name', 'last_name', 'phone', 'is_active'];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $setParts[] = "$field = ?";
                $params[] = $data[$field];
            }
        }

        if (isset($data['password']) && !empty($data['password'])) {
            $setParts[] = "password_hash = ?";
            $params[] = password_hash($data['password'], PASSWORD_DEFAULT);
        }

        if (empty($setParts)) {
            return false;
        }

        $params[] = $id;
        $sql = "UPDATE users SET " . implode(', ', $setParts) . " WHERE id = ?";

        return $this->query($sql, $params) !== false;
    }

    /**
     * Delete user (soft delete)
     */
    public function delete($id) {
        $sql = "UPDATE users SET is_active = 0 WHERE id = ?";
        return $this->query($sql, [$id]) !== false;
    }

    /**
     * Update last login time
     */
    public function updateLastLogin($id) {
        $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
        return $this->query($sql, [$id]) !== false;
    }

    /**
     * Check if username exists
     */
    public function usernameExists($username, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM users WHERE username = ?";
        $params = [$username];

        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }

        $result = $this->query($sql, $params);
        return $result ? $result->fetch_assoc()['count'] > 0 : false;
    }

    /**
     * Check if email exists
     */
    public function emailExists($email, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM users WHERE email = ?";
        $params = [$email];

        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }

        $result = $this->query($sql, $params);
        return $result ? $result->fetch_assoc()['count'] > 0 : false;
    }

    /**
     * Get users by role
     */
    public function getByRole($role) {
        $sql = "SELECT * FROM users WHERE role = ? AND is_active = 1 ORDER BY first_name, last_name";
        $result = $this->query($sql, [$role]);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }

    /**
     * Verify password
     */
    public function verifyPassword($username, $password) {
        $user = $this->findByUsername($username);
        if ($user) {
            return password_verify($password, $user['password_hash']);
        }
        return false;
    }

    /**
     * Get user role
     */
    public function getRole($username) {
        $user = $this->findByUsername($username);
        return $user ? $user['role'] : null;
    }

    /**
     * Get user statistics
     */
    public function getStats() {
        $stats = [];

        // Total users
        $result = $this->query("SELECT COUNT(*) as total FROM users WHERE is_active = 1");
        $stats['total_users'] = $result ? $result->fetch_assoc()['total'] : 0;

        // Admin count
        $result = $this->query("SELECT COUNT(*) as total FROM users WHERE role = 'admin' AND is_active = 1");
        $stats['admin_count'] = $result ? $result->fetch_assoc()['total'] : 0;

        // Staff count
        $result = $this->query("SELECT COUNT(*) as total FROM users WHERE role = 'staff' AND is_active = 1");
        $stats['staff_count'] = $result ? $result->fetch_assoc()['total'] : 0;

        // Recent users (last 30 days)
        $result = $this->query("SELECT COUNT(*) as total FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) AND is_active = 1");
        $stats['recent_users'] = $result ? $result->fetch_assoc()['total'] : 0;

        return $stats;
    }
}