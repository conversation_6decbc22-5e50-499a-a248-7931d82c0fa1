<?php
/**
 * Quick Test - Verify System is Working
 */

// Start session
session_start();

// Include configuration
require_once 'config/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Quick Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head><body class='bg-light p-4'>";

echo "<div class='container'>";
echo "<h2>Quick System Test</h2>";

// Test 1: Configuration
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>1. Configuration</h5></div>";
echo "<div class='card-body'>";
echo "<p>✓ Environment: " . ENVIRONMENT . "</p>";
echo "<p>✓ Base URL: " . BASE_URL . "</p>";
echo "<p>✓ Database: " . DB_NAME . "</p>";
echo "</div></div>";

// Test 2: Database
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>2. Database Connection</h5></div>";
echo "<div class='card-body'>";
try {
    $db = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($db->connect_errno) {
        echo "<p class='text-danger'>✗ Connection failed: " . $db->connect_error . "</p>";
    } else {
        echo "<p class='text-success'>✓ Database connected successfully</p>";
        
        // Check users table
        $result = $db->query("SELECT COUNT(*) as count FROM users");
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            echo "<p class='text-success'>✓ Users table exists ($count users)</p>";
        }
        $db->close();
    }
} catch (Exception $e) {
    echo "<p class='text-danger'>✗ Database error: " . $e->getMessage() . "</p>";
}
echo "</div></div>";

// Test 3: Core Files
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>3. Core Files</h5></div>";
echo "<div class='card-body'>";
$files = [
    'application/core/Auth.php',
    'application/controllers/LoginController.php',
    'application/controllers/DashboardController.php',
    'application/views/auth/login.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<p class='text-success'>✓ $file</p>";
    } else {
        echo "<p class='text-danger'>✗ $file missing</p>";
    }
}
echo "</div></div>";

// Test 4: Authentication
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>4. Authentication Test</h5></div>";
echo "<div class='card-body'>";

require_once 'application/core/Auth.php';

echo "<p>Current login status: " . (Auth::isLoggedIn() ? 'Logged In' : 'Not Logged In') . "</p>";

if (isset($_POST['test_login'])) {
    $result = Auth::attempt('admin', 'admin123');
    if ($result['success']) {
        echo "<div class='alert alert-success'>✓ Login test successful!</div>";
    } else {
        echo "<div class='alert alert-danger'>✗ Login test failed: " . $result['message'] . "</div>";
    }
}

echo "<form method='POST'>";
echo "<button type='submit' name='test_login' class='btn btn-primary'>Test Login (admin/admin123)</button>";
echo "</form>";

echo "</div></div>";

// Navigation
echo "<div class='card'>";
echo "<div class='card-header'><h5>Navigation</h5></div>";
echo "<div class='card-body'>";
echo "<a href='clear_session.php' class='btn btn-warning me-2'>Clear Session</a>";
echo "<a href='index.php' class='btn btn-success me-2'>Go to App</a>";
echo "<a href='test_setup.php' class='btn btn-info'>Full Setup Test</a>";
echo "</div></div>";

echo "</div>";
echo "</body></html>";
?>
