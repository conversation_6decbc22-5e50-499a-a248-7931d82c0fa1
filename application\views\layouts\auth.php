<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? APP_NAME ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?= $this->asset('css/auth.css') ?>" rel="stylesheet">
    
    <meta name="csrf-token" content="<?= $csrf_token ?>">
</head>
<body class="auth-body">
    <div class="auth-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-4">
                    <div class="auth-card">
                        <div class="auth-header text-center mb-4">
                            <h2 class="auth-title"><?= APP_NAME ?></h2>
                            <p class="auth-subtitle">Invoice & Stock Management System</p>
                        </div>
                        
                        <!-- Flash Messages -->
                        <?php if (!empty($flash)): ?>
                            <?php foreach ($flash as $type => $message): ?>
                                <div class="alert alert-<?= $type === 'error' ? 'danger' : $type ?> alert-dismissible fade show" role="alert">
                                    <?= $this->escape($message) ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        
                        <!-- Content -->
                        <?= $content ?>
                        
                        <div class="auth-footer text-center mt-4">
                            <small class="text-muted">
                                &copy; <?= date('Y') ?> <?= APP_AUTHOR ?>. All rights reserved.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?= $this->asset('js/auth.js') ?>"></script>
</body>
</html>
