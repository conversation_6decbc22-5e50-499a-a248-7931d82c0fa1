/* Custom CSS for Invoice & Stock Management System */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --sidebar-width: 250px;
    --sidebar-collapsed-width: 60px;
}

/* Body and Layout */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f4f6f9;
}

.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
}

/* Sidebar Styles */
.sidebar {
    min-width: var(--sidebar-width);
    max-width: var(--sidebar-width);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    transition: all 0.3s;
    min-height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
}

.sidebar.collapsed {
    min-width: var(--sidebar-collapsed-width);
    max-width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 20px;
    background: rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h3 {
    color: #fff;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.sidebar.collapsed .sidebar-header h3 {
    display: none;
}

.sidebar ul.components {
    padding: 20px 0;
}

.sidebar ul li {
    margin-bottom: 5px;
}

.sidebar ul li a {
    padding: 12px 20px;
    font-size: 14px;
    display: block;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s;
    border-left: 3px solid transparent;
}

.sidebar ul li a:hover,
.sidebar ul li.active > a {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
    border-left-color: #fff;
}

.sidebar ul li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar.collapsed ul li a {
    text-align: center;
    padding: 12px 0;
}

.sidebar.collapsed ul li a span {
    display: none;
}

/* Dropdown menu */
.sidebar ul ul {
    background: rgba(0, 0, 0, 0.1);
    padding: 0;
}

.sidebar ul ul li a {
    padding: 10px 40px;
    font-size: 13px;
    border-left: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.sidebar.collapsed ul ul {
    display: none !important;
}

/* Content Area */
.content {
    width: 100%;
    padding: 0;
    min-height: 100vh;
    transition: all 0.3s;
    margin-left: var(--sidebar-width);
}

.sidebar.collapsed + .content {
    margin-left: var(--sidebar-collapsed-width);
}

/* Top Navigation */
.content .navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid #e3e6f0;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    border-radius: 10px 10px 0 0 !important;
}

/* Border Cards */
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}

.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}

.border-left-danger {
    border-left: 0.25rem solid var(--danger-color) !important;
}

/* Tables */
.table {
    color: #5a5c69;
}

.table thead th {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-responsive {
    border-radius: 10px;
}

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-sm {
    font-size: 0.8rem;
    padding: 0.375rem 0.75rem;
}

.btn-xs {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

/* Forms */
.form-control {
    border-radius: 6px;
    border: 1px solid #d1d3e2;
    transition: all 0.3s;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select {
    border-radius: 6px;
    border: 1px solid #d1d3e2;
}

/* Badges */
.badge {
    font-weight: 500;
    border-radius: 4px;
}

/* Alerts */
.alert {
    border-radius: 8px;
    border: none;
}

/* Text Utilities */
.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        margin-left: -var(--sidebar-width);
    }
    
    .sidebar.show {
        margin-left: 0;
    }
    
    .content {
        margin-left: 0;
    }
    
    .sidebar.collapsed + .content {
        margin-left: 0;
    }
}

/* Dashboard Specific */
.dashboard .card-body {
    padding: 1.5rem;
}

.dashboard .row.no-gutters {
    margin-right: 0;
    margin-left: 0;
}

.dashboard .row.no-gutters > [class*="col-"] {
    padding-right: 0;
    padding-left: 0;
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Scrollbar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .content {
        margin-left: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
