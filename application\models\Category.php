<?php
/**
 * Category Model
 * Handles product category-related database operations
 */

class Category extends Model {
    protected $table = 'categories';
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * Get all categories
     */
    public function getAll($activeOnly = true) {
        $whereClause = $activeOnly ? "WHERE is_active = 1" : "";
        $sql = "SELECT * FROM categories $whereClause ORDER BY name ASC";
        
        $result = $this->query($sql);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
    
    /**
     * Find category by ID
     */
    public function findById($id) {
        $sql = "SELECT * FROM categories WHERE id = ?";
        $result = $this->query($sql, [$id]);
        return $result ? $result->fetch_assoc() : null;
    }
    
    /**
     * Create new category
     */
    public function create($data) {
        $sql = "INSERT INTO categories (name, description) VALUES (?, ?)";
        $params = [$data['name'], $data['description'] ?? null];
        
        $result = $this->query($sql, $params);
        return $result ? $this->db->insert_id : false;
    }
    
    /**
     * Update category
     */
    public function update($id, $data) {
        $setParts = [];
        $params = [];
        
        $allowedFields = ['name', 'description', 'is_active'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $setParts[] = "$field = ?";
                $params[] = $data[$field];
            }
        }
        
        if (empty($setParts)) {
            return false;
        }
        
        $params[] = $id;
        $sql = "UPDATE categories SET " . implode(', ', $setParts) . " WHERE id = ?";
        
        return $this->query($sql, $params) !== false;
    }
    
    /**
     * Delete category (soft delete)
     */
    public function delete($id) {
        $sql = "UPDATE categories SET is_active = 0 WHERE id = ?";
        return $this->query($sql, [$id]) !== false;
    }
    
    /**
     * Get categories with product count
     */
    public function getCategoriesWithProductCount() {
        $sql = "SELECT c.*, COUNT(p.id) as product_count 
                FROM categories c 
                LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1 
                WHERE c.is_active = 1 
                GROUP BY c.id 
                ORDER BY c.name ASC";
        
        $result = $this->query($sql);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
    
    /**
     * Check if category name exists
     */
    public function nameExists($name, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM categories WHERE name = ?";
        $params = [$name];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->query($sql, $params);
        return $result ? $result->fetch_assoc()['count'] > 0 : false;
    }
}
