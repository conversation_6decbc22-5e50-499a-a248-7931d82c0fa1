/**
 * Authentication JavaScript
 * Login and authentication related functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeAuthForm();
    initializePasswordToggle();
    initializeFormValidation();
    initializeKeyboardShortcuts();
});

/**
 * Initialize Authentication Form
 */
function initializeAuthForm() {
    const authForm = document.querySelector('.auth-form');
    if (!authForm) return;
    
    authForm.addEventListener('submit', function(e) {
        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.innerHTML;
        
        // Show loading state
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Logging in...';
        submitButton.disabled = true;
        
        // Add loading class to form
        this.classList.add('loading');
        
        // If form validation fails, restore button
        setTimeout(function() {
            if (!authForm.checkValidity()) {
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
                authForm.classList.remove('loading');
            }
        }, 100);
    });
}

/**
 * Initialize Password Toggle
 */
function initializePasswordToggle() {
    const togglePassword = document.getElementById('togglePassword');
    const passwordField = document.getElementById('password');
    const toggleIcon = document.getElementById('togglePasswordIcon');
    
    if (togglePassword && passwordField && toggleIcon) {
        togglePassword.addEventListener('click', function() {
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);
            
            // Toggle icon
            if (type === 'password') {
                toggleIcon.className = 'bi bi-eye';
                this.setAttribute('title', 'Show password');
            } else {
                toggleIcon.className = 'bi bi-eye-slash';
                this.setAttribute('title', 'Hide password');
            }
        });
    }
}

/**
 * Initialize Form Validation
 */
function initializeFormValidation() {
    const usernameField = document.getElementById('username');
    const passwordField = document.getElementById('password');
    
    if (usernameField) {
        usernameField.addEventListener('input', function() {
            validateUsername(this);
        });
        
        usernameField.addEventListener('blur', function() {
            validateUsername(this);
        });
    }
    
    if (passwordField) {
        passwordField.addEventListener('input', function() {
            validatePassword(this);
        });
        
        passwordField.addEventListener('blur', function() {
            validatePassword(this);
        });
    }
}

/**
 * Validate Username
 */
function validateUsername(field) {
    const value = field.value.trim();
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    
    // Remove existing validation classes
    field.classList.remove('is-valid', 'is-invalid');
    
    if (value.length === 0) {
        field.classList.add('is-invalid');
        if (feedback) feedback.textContent = 'Username is required';
        return false;
    }
    
    if (value.length < 3) {
        field.classList.add('is-invalid');
        if (feedback) feedback.textContent = 'Username must be at least 3 characters';
        return false;
    }
    
    if (!/^[a-zA-Z0-9_]+$/.test(value)) {
        field.classList.add('is-invalid');
        if (feedback) feedback.textContent = 'Username can only contain letters, numbers, and underscores';
        return false;
    }
    
    field.classList.add('is-valid');
    return true;
}

/**
 * Validate Password
 */
function validatePassword(field) {
    const value = field.value;
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    
    // Remove existing validation classes
    field.classList.remove('is-valid', 'is-invalid');
    
    if (value.length === 0) {
        field.classList.add('is-invalid');
        if (feedback) feedback.textContent = 'Password is required';
        return false;
    }
    
    if (value.length < 6) {
        field.classList.add('is-invalid');
        if (feedback) feedback.textContent = 'Password must be at least 6 characters';
        return false;
    }
    
    field.classList.add('is-valid');
    return true;
}

/**
 * Initialize Keyboard Shortcuts
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Enter key on username field moves to password
        if (e.target.id === 'username' && e.key === 'Enter') {
            e.preventDefault();
            const passwordField = document.getElementById('password');
            if (passwordField) {
                passwordField.focus();
            }
        }
        
        // Ctrl+Enter submits the form
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            const authForm = document.querySelector('.auth-form');
            if (authForm) {
                authForm.submit();
            }
        }
    });
}

/**
 * Show Login Error
 */
function showLoginError(message) {
    const alertContainer = document.querySelector('.auth-card');
    if (alertContainer) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // Remove existing alerts
        const existingAlerts = alertContainer.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());
        
        // Add new alert
        const authHeader = alertContainer.querySelector('.auth-header');
        authHeader.insertAdjacentHTML('afterend', alertHtml);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            const alert = alertContainer.querySelector('.alert');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }
}

/**
 * Remember Me Functionality
 */
function initializeRememberMe() {
    const rememberCheckbox = document.getElementById('remember');
    const usernameField = document.getElementById('username');
    
    if (rememberCheckbox && usernameField) {
        // Load saved username
        const savedUsername = localStorage.getItem('rememberedUsername');
        if (savedUsername) {
            usernameField.value = savedUsername;
            rememberCheckbox.checked = true;
        }
        
        // Save username when form is submitted
        const authForm = document.querySelector('.auth-form');
        if (authForm) {
            authForm.addEventListener('submit', function() {
                if (rememberCheckbox.checked) {
                    localStorage.setItem('rememberedUsername', usernameField.value);
                } else {
                    localStorage.removeItem('rememberedUsername');
                }
            });
        }
    }
}

/**
 * Auto-fill Demo Credentials
 */
function fillDemoCredentials(type) {
    const usernameField = document.getElementById('username');
    const passwordField = document.getElementById('password');
    
    if (usernameField && passwordField) {
        if (type === 'admin') {
            usernameField.value = 'admin';
            passwordField.value = 'admin123';
        } else if (type === 'staff') {
            usernameField.value = 'staff';
            passwordField.value = 'staff123';
        }
        
        // Trigger validation
        validateUsername(usernameField);
        validatePassword(passwordField);
        
        // Focus on submit button
        const submitButton = document.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.focus();
        }
    }
}

/**
 * Caps Lock Detection
 */
function initializeCapsLockDetection() {
    const passwordField = document.getElementById('password');
    
    if (passwordField) {
        let capsLockWarning = null;
        
        passwordField.addEventListener('keydown', function(e) {
            const capsLock = e.getModifierState && e.getModifierState('CapsLock');
            
            if (capsLock && !capsLockWarning) {
                capsLockWarning = document.createElement('div');
                capsLockWarning.className = 'alert alert-warning alert-sm mt-2';
                capsLockWarning.innerHTML = '<i class="bi bi-exclamation-triangle me-2"></i>Caps Lock is on';
                passwordField.parentNode.appendChild(capsLockWarning);
            } else if (!capsLock && capsLockWarning) {
                capsLockWarning.remove();
                capsLockWarning = null;
            }
        });
    }
}

/**
 * Session Timeout Warning
 */
function initializeSessionTimeout() {
    const timeoutWarning = document.querySelector('[data-timeout]');
    if (timeoutWarning) {
        // Auto-hide timeout warning after 10 seconds
        setTimeout(function() {
            const alert = new bootstrap.Alert(timeoutWarning);
            alert.close();
        }, 10000);
    }
}

// Initialize additional features
document.addEventListener('DOMContentLoaded', function() {
    initializeRememberMe();
    initializeCapsLockDetection();
    initializeSessionTimeout();
    
    // Add click handlers for demo credential buttons
    const demoCards = document.querySelectorAll('.auth-info .card');
    demoCards.forEach(function(card, index) {
        card.addEventListener('click', function() {
            const type = index === 0 ? 'admin' : 'staff';
            fillDemoCredentials(type);
        });
        
        // Add hover effect
        card.style.cursor = 'pointer';
        card.setAttribute('title', 'Click to auto-fill credentials');
    });
});

/**
 * Form Animation Effects
 */
function addFormAnimations() {
    const formGroups = document.querySelectorAll('.mb-3');
    
    formGroups.forEach(function(group, index) {
        group.style.opacity = '0';
        group.style.transform = 'translateY(20px)';
        group.style.transition = 'all 0.3s ease';
        
        setTimeout(function() {
            group.style.opacity = '1';
            group.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Add animations when page loads
window.addEventListener('load', function() {
    addFormAnimations();
});
