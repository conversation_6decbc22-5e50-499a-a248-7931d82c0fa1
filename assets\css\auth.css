/* Authentication Page Styles */

.auth-body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.auth-container {
    width: 100%;
    padding: 20px;
}

.auth-card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin: 20px 0;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-header {
    margin-bottom: 30px;
}

.auth-title {
    color: #333;
    font-weight: 700;
    font-size: 2rem;
    margin-bottom: 10px;
}

.auth-subtitle {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.auth-form {
    margin-bottom: 20px;
}

.auth-form .form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.auth-form .form-label i {
    margin-right: 8px;
    color: #667eea;
}

.auth-form .form-control {
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.auth-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

.auth-form .form-control.is-invalid {
    border-color: #dc3545;
}

.auth-form .input-group-text {
    background: #f8f9fa;
    border: 2px solid #e1e5e9;
    border-left: none;
}

.auth-form .btn-outline-secondary {
    border-color: #e1e5e9;
    color: #666;
}

.auth-form .btn-outline-secondary:hover {
    background: #f8f9fa;
    border-color: #667eea;
    color: #667eea;
}

.auth-form .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
}

.auth-form .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.auth-form .form-check-label {
    font-size: 14px;
    color: #666;
}

.auth-form .form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.auth-info {
    background: #f8f9fc;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.auth-info .card {
    border: 1px solid #e3e6f0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.auth-info .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.auth-info .card-body {
    padding: 15px;
}

.auth-features {
    margin-top: 20px;
}

.auth-features .col-6,
.auth-features .col-md-3 {
    margin-bottom: 15px;
}

.auth-features i {
    margin-bottom: 8px;
}

.auth-features small {
    color: #666;
    font-weight: 500;
}

.auth-footer {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e3e6f0;
}

.auth-footer small {
    color: #999;
}

/* Alert Styles */
.alert {
    border-radius: 8px;
    border: none;
    margin-bottom: 20px;
}

.alert-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: #fff;
}

.alert-warning {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
    color: #fff;
}

.alert-success {
    background: linear-gradient(135deg, #48cab2 0%, #2dd4bf 100%);
    color: #fff;
}

.alert-info {
    background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%);
    color: #fff;
}

.alert i {
    margin-right: 8px;
}

/* Invalid Feedback */
.invalid-feedback {
    font-size: 13px;
    margin-top: 5px;
}

/* Responsive Design */
@media (max-width: 576px) {
    .auth-card {
        padding: 30px 20px;
        margin: 10px 0;
    }
    
    .auth-title {
        font-size: 1.5rem;
    }
    
    .auth-info {
        padding: 15px;
    }
    
    .auth-features .col-6 {
        margin-bottom: 20px;
    }
}

/* Loading Animation */
.auth-loading {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.auth-form.loading .btn-primary {
    opacity: 0.6;
    pointer-events: none;
}

.auth-form.loading .auth-loading {
    display: block;
}

/* Password Strength Indicator */
.password-strength {
    height: 4px;
    border-radius: 2px;
    margin-top: 5px;
    background: #e1e5e9;
    overflow: hidden;
}

.password-strength-bar {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.password-strength-weak .password-strength-bar {
    width: 33%;
    background: #dc3545;
}

.password-strength-medium .password-strength-bar {
    width: 66%;
    background: #ffc107;
}

.password-strength-strong .password-strength-bar {
    width: 100%;
    background: #28a745;
}

/* Floating Labels Effect */
.floating-label {
    position: relative;
}

.floating-label .form-control {
    padding-top: 20px;
    padding-bottom: 8px;
}

.floating-label .form-label {
    position: absolute;
    top: 12px;
    left: 15px;
    transition: all 0.3s ease;
    pointer-events: none;
    color: #999;
}

.floating-label .form-control:focus + .form-label,
.floating-label .form-control:not(:placeholder-shown) + .form-label {
    top: 4px;
    font-size: 12px;
    color: #667eea;
    font-weight: 600;
}
