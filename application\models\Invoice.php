<?php
/**
 * Invoice Model
 * Handles invoice-related database operations
 */

class Invoice extends Model {
    protected $table = 'invoices';
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * Get all invoices with pagination
     */
    public function getAll($page = 1, $limit = RECORDS_PER_PAGE, $search = '', $status = '') {
        $offset = ($page - 1) * $limit;
        
        $whereClause = "WHERE 1=1";
        $params = [];
        
        if (!empty($search)) {
            $whereClause .= " AND (i.invoice_number LIKE ? OR c.name LIKE ?)";
            $searchTerm = "%$search%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        if (!empty($status)) {
            $whereClause .= " AND i.status = ?";
            $params[] = $status;
        }
        
        $sql = "SELECT i.*, c.name as customer_name, c.email as customer_email, 
                       u.username as created_by
                FROM invoices i 
                LEFT JOIN customers c ON i.customer_id = c.id 
                LEFT JOIN users u ON i.user_id = u.id 
                $whereClause 
                ORDER BY i.created_at DESC 
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $result = $this->query($sql, $params);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
    
    /**
     * Get total invoice count
     */
    public function getTotalCount($search = '', $status = '') {
        $whereClause = "WHERE 1=1";
        $params = [];
        
        if (!empty($search)) {
            $whereClause .= " AND (i.invoice_number LIKE ? OR c.name LIKE ?)";
            $searchTerm = "%$search%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        if (!empty($status)) {
            $whereClause .= " AND i.status = ?";
            $params[] = $status;
        }
        
        $sql = "SELECT COUNT(*) as total 
                FROM invoices i 
                LEFT JOIN customers c ON i.customer_id = c.id 
                $whereClause";
        
        $result = $this->query($sql, $params);
        return $result ? $result->fetch_assoc()['total'] : 0;
    }
    
    /**
     * Find invoice by ID with details
     */
    public function findById($id) {
        $sql = "SELECT i.*, c.name as customer_name, c.email as customer_email, 
                       c.phone as customer_phone, c.address as customer_address,
                       c.city as customer_city, c.state as customer_state,
                       c.pincode as customer_pincode, c.gstin as customer_gstin,
                       u.username as created_by
                FROM invoices i 
                LEFT JOIN customers c ON i.customer_id = c.id 
                LEFT JOIN users u ON i.user_id = u.id 
                WHERE i.id = ?";
        
        $result = $this->query($sql, [$id]);
        return $result ? $result->fetch_assoc() : null;
    }
    
    /**
     * Find invoice by invoice number
     */
    public function findByInvoiceNumber($invoiceNumber) {
        $sql = "SELECT * FROM invoices WHERE invoice_number = ?";
        $result = $this->query($sql, [$invoiceNumber]);
        return $result ? $result->fetch_assoc() : null;
    }
    
    /**
     * Create new invoice
     */
    public function create($data) {
        $sql = "INSERT INTO invoices (invoice_number, customer_id, user_id, invoice_date, 
                due_date, subtotal, tax_amount, discount_amount, total_amount, status, notes, terms_conditions) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $data['invoice_number'],
            $data['customer_id'],
            $data['user_id'],
            $data['invoice_date'],
            $data['due_date'],
            $data['subtotal'],
            $data['tax_amount'],
            $data['discount_amount'] ?? 0,
            $data['total_amount'],
            $data['status'] ?? 'draft',
            $data['notes'] ?? null,
            $data['terms_conditions'] ?? null
        ];
        
        $result = $this->query($sql, $params);
        return $result ? $this->db->insert_id : false;
    }
    
    /**
     * Update invoice
     */
    public function update($id, $data) {
        $setParts = [];
        $params = [];
        
        $allowedFields = ['customer_id', 'invoice_date', 'due_date', 'subtotal', 
                         'tax_amount', 'discount_amount', 'total_amount', 'status', 'notes', 'terms_conditions'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $setParts[] = "$field = ?";
                $params[] = $data[$field];
            }
        }
        
        if (empty($setParts)) {
            return false;
        }
        
        $params[] = $id;
        $sql = "UPDATE invoices SET " . implode(', ', $setParts) . " WHERE id = ?";
        
        return $this->query($sql, $params) !== false;
    }
    
    /**
     * Delete invoice
     */
    public function delete($id) {
        // First delete invoice items
        $this->query("DELETE FROM invoice_items WHERE invoice_id = ?", [$id]);
        
        // Then delete payments
        $this->query("DELETE FROM payments WHERE invoice_id = ?", [$id]);
        
        // Finally delete invoice
        $sql = "DELETE FROM invoices WHERE id = ?";
        return $this->query($sql, [$id]) !== false;
    }
    
    /**
     * Get invoice items
     */
    public function getItems($invoiceId) {
        $sql = "SELECT ii.*, p.name as product_name, p.sku 
                FROM invoice_items ii 
                LEFT JOIN products p ON ii.product_id = p.id 
                WHERE ii.invoice_id = ? 
                ORDER BY ii.id";
        
        $result = $this->query($sql, [$invoiceId]);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
    
    /**
     * Add invoice item
     */
    public function addItem($invoiceId, $item) {
        $sql = "INSERT INTO invoice_items (invoice_id, product_id, product_name, quantity, unit_price, tax_rate, line_total) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $invoiceId,
            $item['product_id'],
            $item['product_name'],
            $item['quantity'],
            $item['unit_price'],
            $item['tax_rate'] ?? TAX_RATE,
            $item['line_total']
        ];
        
        return $this->query($sql, $params) !== false;
    }
    
    /**
     * Clear invoice items
     */
    public function clearItems($invoiceId) {
        $sql = "DELETE FROM invoice_items WHERE invoice_id = ?";
        return $this->query($sql, [$invoiceId]) !== false;
    }
    
    /**
     * Generate unique invoice number
     */
    public function generateInvoiceNumber() {
        $prefix = INVOICE_PREFIX;
        $year = date('Y');
        $month = date('m');
        
        // Get the last invoice number for this month
        $sql = "SELECT invoice_number FROM invoices 
                WHERE invoice_number LIKE ? 
                ORDER BY invoice_number DESC LIMIT 1";
        
        $pattern = $prefix . $year . $month . '%';
        $result = $this->query($sql, [$pattern]);
        
        if ($result && $row = $result->fetch_assoc()) {
            $lastNumber = $row['invoice_number'];
            $sequence = intval(substr($lastNumber, -4)) + 1;
        } else {
            $sequence = 1;
        }
        
        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
    
    /**
     * Get invoice statistics
     */
    public function getStats() {
        $stats = [];
        
        // Total invoices
        $result = $this->query("SELECT COUNT(*) as total FROM invoices");
        $stats['total_invoices'] = $result ? $result->fetch_assoc()['total'] : 0;
        
        // Draft invoices
        $result = $this->query("SELECT COUNT(*) as total FROM invoices WHERE status = 'draft'");
        $stats['draft_invoices'] = $result ? $result->fetch_assoc()['total'] : 0;
        
        // Paid invoices
        $result = $this->query("SELECT COUNT(*) as total FROM invoices WHERE status = 'paid'");
        $stats['paid_invoices'] = $result ? $result->fetch_assoc()['total'] : 0;
        
        // Overdue invoices
        $result = $this->query("SELECT COUNT(*) as total FROM invoices WHERE status = 'overdue' OR (status != 'paid' AND due_date < CURDATE())");
        $stats['overdue_invoices'] = $result ? $result->fetch_assoc()['total'] : 0;
        
        // Total revenue
        $result = $this->query("SELECT SUM(total_amount) as total FROM invoices WHERE status = 'paid'");
        $stats['total_revenue'] = $result ? ($result->fetch_assoc()['total'] ?? 0) : 0;
        
        // Pending amount
        $result = $this->query("SELECT SUM(total_amount - paid_amount) as total FROM invoices WHERE status != 'paid'");
        $stats['pending_amount'] = $result ? ($result->fetch_assoc()['total'] ?? 0) : 0;
        
        // This month's revenue
        $result = $this->query("SELECT SUM(total_amount) as total FROM invoices WHERE status = 'paid' AND MONTH(invoice_date) = MONTH(CURDATE()) AND YEAR(invoice_date) = YEAR(CURDATE())");
        $stats['monthly_revenue'] = $result ? ($result->fetch_assoc()['total'] ?? 0) : 0;
        
        return $stats;
    }
    
    /**
     * Get overdue invoices
     */
    public function getOverdueInvoices() {
        $sql = "SELECT i.*, c.name as customer_name, c.email as customer_email 
                FROM invoices i 
                LEFT JOIN customers c ON i.customer_id = c.id 
                WHERE (i.status = 'overdue' OR (i.status != 'paid' AND i.due_date < CURDATE()))
                ORDER BY i.due_date ASC";
        
        $result = $this->query($sql);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
    
    /**
     * Update invoice status
     */
    public function updateStatus($id, $status) {
        $sql = "UPDATE invoices SET status = ? WHERE id = ?";
        return $this->query($sql, [$status, $id]) !== false;
    }
    
    /**
     * Add payment to invoice
     */
    public function addPayment($invoiceId, $amount, $paymentMethod, $referenceNumber = null, $notes = null) {
        // Insert payment record
        $sql = "INSERT INTO payments (invoice_id, payment_date, amount, payment_method, reference_number, notes, user_id) 
                VALUES (?, CURDATE(), ?, ?, ?, ?, ?)";
        
        $params = [$invoiceId, $amount, $paymentMethod, $referenceNumber, $notes, Auth::getUserId()];
        $result = $this->query($sql, $params);
        
        if ($result) {
            // Update invoice paid amount
            $sql = "UPDATE invoices SET paid_amount = paid_amount + ? WHERE id = ?";
            $this->query($sql, [$amount, $invoiceId]);
            
            // Check if invoice is fully paid
            $invoice = $this->findById($invoiceId);
            if ($invoice && $invoice['paid_amount'] >= $invoice['total_amount']) {
                $this->updateStatus($invoiceId, 'paid');
            }
            
            return true;
        }
        
        return false;
    }
}
