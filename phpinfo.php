<?php
echo "<h1>PHP Test</h1>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";
echo "<p>Current Directory: " . __DIR__ . "</p>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Request URI: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>Server Name: " . $_SERVER['SERVER_NAME'] . "</p>";

echo "<h2>File Check</h2>";
$files = ['index.php', 'config/config.php', 'application/database_setup.php'];
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ $file exists</p>";
    } else {
        echo "<p style='color: red;'>✗ $file missing</p>";
    }
}

echo "<h2>Database Test</h2>";
try {
    $db = new mysqli('localhost', 'root', '', 'gym_invoice_system');
    if ($db->connect_errno) {
        echo "<p style='color: red;'>Database connection failed: " . $db->connect_error . "</p>";
    } else {
        echo "<p style='color: green;'>Database connection successful</p>";
        $db->close();
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}

echo "<h2>Links</h2>";
echo "<p><a href='test_setup.php'>Run Setup Test</a></p>";
echo "<p><a href='index.php'>Go to Application</a></p>";
?>
