<div class="dashboard">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Dashboard</h1>
            <p class="text-muted">Welcome back, <?= $current_user['first_name'] ?? $current_user['username'] ?>!</p>
        </div>
        <div>
            <button class="btn btn-outline-secondary btn-sm" onclick="refreshDashboard()">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
            <?php if ($is_admin): ?>
            <button class="btn btn-outline-danger btn-sm" onclick="clearCache()">
                <i class="bi bi-trash"></i> Clear Cache
            </button>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <!-- Revenue Stats -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $this->currency($stats['total_revenue']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-currency-rupee fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Monthly Revenue -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                This Month
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $this->currency($stats['monthly_revenue']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-calendar-month fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Pending Amount -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Amount
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $this->currency($stats['pending_amount']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Total Customers -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Customers
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['total_customers']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-people fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Stats Row -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <!-- Sales Chart -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Sales Overview</h6>
                    <div class="dropdown no-arrow">
                        <select class="form-select form-select-sm" id="salesPeriod" onchange="updateSalesChart()">
                            <option value="monthly">Monthly</option>
                            <option value="weekly">Weekly</option>
                            <option value="daily">Daily</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="salesChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Quick Stats -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Stats</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border-bottom pb-2">
                                <h4 class="text-primary"><?= $stats['total_invoices'] ?></h4>
                                <small class="text-muted">Total Invoices</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border-bottom pb-2">
                                <h4 class="text-success"><?= $stats['paid_invoices'] ?></h4>
                                <small class="text-muted">Paid Invoices</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border-bottom pb-2">
                                <h4 class="text-warning"><?= $stats['draft_invoices'] ?></h4>
                                <small class="text-muted">Draft Invoices</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border-bottom pb-2">
                                <h4 class="text-danger"><?= $stats['overdue_invoices'] ?></h4>
                                <small class="text-muted">Overdue</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info"><?= $stats['total_products'] ?></h4>
                            <small class="text-muted">Products</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning"><?= $stats['low_stock_products'] ?></h4>
                            <small class="text-muted">Low Stock</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity Row -->
    <div class="row">
        <!-- Recent Invoices -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Invoices</h6>
                    <a href="<?= $this->url('?page=invoices') ?>" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if (!empty($recent_invoices)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Invoice #</th>
                                        <th>Customer</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_invoices as $invoice): ?>
                                    <tr>
                                        <td>
                                            <a href="<?= $this->url('?page=invoices/view&id=' . $invoice['id']) ?>">
                                                <?= $this->escape($invoice['invoice_number']) ?>
                                            </a>
                                        </td>
                                        <td><?= $this->escape($invoice['customer_name']) ?></td>
                                        <td><?= $this->currency($invoice['total_amount']) ?></td>
                                        <td>
                                            <span class="badge bg-<?= $invoice['status'] === 'paid' ? 'success' : ($invoice['status'] === 'overdue' ? 'danger' : 'warning') ?>">
                                                <?= ucfirst($invoice['status']) ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">No recent invoices found.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Low Stock Alert -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-warning">Low Stock Alert</h6>
                    <a href="<?= $this->url('?page=stock/low-stock') ?>" class="btn btn-sm btn-outline-warning">View All</a>
                </div>
                <div class="card-body">
                    <?php if (!empty($low_stock_products)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Current Stock</th>
                                        <th>Min Level</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($low_stock_products as $product): ?>
                                    <tr>
                                        <td><?= $this->escape($product['name']) ?></td>
                                        <td>
                                            <span class="badge bg-<?= $product['stock_quantity'] == 0 ? 'danger' : 'warning' ?>">
                                                <?= $product['stock_quantity'] ?>
                                            </span>
                                        </td>
                                        <td><?= $product['min_stock_level'] ?></td>
                                        <td>
                                            <a href="<?= $this->url('?page=stock/edit&id=' . $product['id']) ?>" class="btn btn-xs btn-outline-primary">
                                                Update
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">All products are well stocked!</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let salesChart;

document.addEventListener('DOMContentLoaded', function() {
    initializeSalesChart();
    loadNotifications();
});

function initializeSalesChart() {
    const ctx = document.getElementById('salesChart').getContext('2d');
    
    salesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Sales Amount',
                data: [],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₹' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Sales: ₹' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });
    
    updateSalesChart();
}

function updateSalesChart() {
    const period = document.getElementById('salesPeriod').value;
    
    fetch(`<?= $this->url('?page=api/dashboard/stats') ?>&chart=sales&period=${period}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const chartData = data.data;
                salesChart.data.labels = chartData.map(item => item.period);
                salesChart.data.datasets[0].data = chartData.map(item => parseFloat(item.total_sales));
                salesChart.update();
            }
        })
        .catch(error => console.error('Error updating chart:', error));
}

function refreshDashboard() {
    location.reload();
}

function clearCache() {
    if (confirm('Are you sure you want to clear the dashboard cache?')) {
        fetch('<?= $this->url('?page=dashboard/clear-cache') ?>', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while clearing cache.');
        });
    }
}

function loadNotifications() {
    // Load low stock notifications
    const lowStockCount = <?= $stats['low_stock_products'] ?>;
    const overdueCount = <?= $stats['overdue_invoices'] ?>;
    
    let notificationCount = 0;
    let notifications = [];
    
    if (lowStockCount > 0) {
        notificationCount += lowStockCount;
        notifications.push(`${lowStockCount} product(s) are running low on stock`);
    }
    
    if (overdueCount > 0) {
        notificationCount += overdueCount;
        notifications.push(`${overdueCount} invoice(s) are overdue`);
    }
    
    // Update notification badge
    const badge = document.getElementById('notificationCount');
    const list = document.getElementById('notificationList');
    
    if (notificationCount > 0) {
        badge.textContent = notificationCount;
        badge.style.display = 'inline';
        
        // Clear existing notifications
        list.innerHTML = '<li><h6 class="dropdown-header">Notifications</h6></li><li><hr class="dropdown-divider"></li>';
        
        // Add notifications
        notifications.forEach(notification => {
            list.innerHTML += `<li><a class="dropdown-item" href="#">${notification}</a></li>`;
        });
    } else {
        badge.style.display = 'none';
    }
}
</script>
