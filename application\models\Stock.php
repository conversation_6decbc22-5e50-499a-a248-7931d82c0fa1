<?php
/**
 * Stock Model
 * Handles stock and product-related database operations
 */

class Stock extends Model {
    protected $table = 'products';
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * Get all products with stock information
     */
    public function getAll($page = 1, $limit = RECORDS_PER_PAGE, $search = '', $categoryId = '') {
        $offset = ($page - 1) * $limit;
        
        $whereClause = "WHERE p.is_active = 1";
        $params = [];
        
        if (!empty($search)) {
            $whereClause .= " AND (p.name LIKE ? OR p.sku LIKE ? OR p.barcode LIKE ?)";
            $searchTerm = "%$search%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        if (!empty($categoryId)) {
            $whereClause .= " AND p.category_id = ?";
            $params[] = $categoryId;
        }
        
        $sql = "SELECT p.*, c.name as category_name, 
                       COALESCE(s.quantity, 0) as stock_quantity,
                       COALESCE(s.reserved_quantity, 0) as reserved_quantity,
                       (COALESCE(s.quantity, 0) - COALESCE(s.reserved_quantity, 0)) as available_quantity
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                LEFT JOIN stock s ON p.id = s.product_id 
                $whereClause 
                ORDER BY p.name ASC 
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $result = $this->query($sql, $params);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
    
    /**
     * Get total product count
     */
    public function getTotalCount($search = '', $categoryId = '') {
        $whereClause = "WHERE p.is_active = 1";
        $params = [];
        
        if (!empty($search)) {
            $whereClause .= " AND (p.name LIKE ? OR p.sku LIKE ? OR p.barcode LIKE ?)";
            $searchTerm = "%$search%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        if (!empty($categoryId)) {
            $whereClause .= " AND p.category_id = ?";
            $params[] = $categoryId;
        }
        
        $sql = "SELECT COUNT(*) as total FROM products p $whereClause";
        $result = $this->query($sql, $params);
        return $result ? $result->fetch_assoc()['total'] : 0;
    }
    
    /**
     * Find product by ID with stock info
     */
    public function findById($id) {
        $sql = "SELECT p.*, c.name as category_name, 
                       COALESCE(s.quantity, 0) as stock_quantity,
                       COALESCE(s.reserved_quantity, 0) as reserved_quantity,
                       (COALESCE(s.quantity, 0) - COALESCE(s.reserved_quantity, 0)) as available_quantity
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                LEFT JOIN stock s ON p.id = s.product_id 
                WHERE p.id = ? AND p.is_active = 1";
        
        $result = $this->query($sql, [$id]);
        return $result ? $result->fetch_assoc() : null;
    }
    
    /**
     * Find product by SKU
     */
    public function findBySku($sku) {
        $sql = "SELECT p.*, COALESCE(s.quantity, 0) as stock_quantity
                FROM products p 
                LEFT JOIN stock s ON p.id = s.product_id 
                WHERE p.sku = ? AND p.is_active = 1";
        
        $result = $this->query($sql, [$sku]);
        return $result ? $result->fetch_assoc() : null;
    }
    
    /**
     * Create new product
     */
    public function create($data) {
        $sql = "INSERT INTO products (name, description, category_id, sku, barcode, unit, 
                purchase_price, selling_price, tax_rate, min_stock_level) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $data['name'],
            $data['description'] ?? null,
            $data['category_id'] ?? null,
            $data['sku'] ?? null,
            $data['barcode'] ?? null,
            $data['unit'] ?? 'pcs',
            $data['purchase_price'] ?? 0,
            $data['selling_price'],
            $data['tax_rate'] ?? TAX_RATE,
            $data['min_stock_level'] ?? LOW_STOCK_THRESHOLD
        ];
        
        $result = $this->query($sql, $params);
        if ($result) {
            $productId = $this->db->insert_id;
            
            // Initialize stock record
            $this->initializeStock($productId, $data['initial_quantity'] ?? 0);
            
            return $productId;
        }
        
        return false;
    }
    
    /**
     * Update product
     */
    public function update($id, $data) {
        $setParts = [];
        $params = [];
        
        $allowedFields = ['name', 'description', 'category_id', 'sku', 'barcode', 'unit',
                         'purchase_price', 'selling_price', 'tax_rate', 'min_stock_level', 'is_active'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $setParts[] = "$field = ?";
                $params[] = $data[$field];
            }
        }
        
        if (empty($setParts)) {
            return false;
        }
        
        $params[] = $id;
        $sql = "UPDATE products SET " . implode(', ', $setParts) . " WHERE id = ?";
        
        return $this->query($sql, $params) !== false;
    }
    
    /**
     * Delete product (soft delete)
     */
    public function delete($id) {
        $sql = "UPDATE products SET is_active = 0 WHERE id = ?";
        return $this->query($sql, [$id]) !== false;
    }
    
    /**
     * Initialize stock for new product
     */
    private function initializeStock($productId, $quantity = 0) {
        $sql = "INSERT INTO stock (product_id, quantity) VALUES (?, ?) 
                ON DUPLICATE KEY UPDATE quantity = VALUES(quantity)";
        return $this->query($sql, [$productId, $quantity]) !== false;
    }
    
    /**
     * Update stock quantity
     */
    public function updateStock($productId, $quantity, $movementType = 'adjustment', $referenceType = 'adjustment', $referenceId = null, $notes = '') {
        // Get current stock
        $currentStock = $this->getStockQuantity($productId);
        
        // Calculate new quantity based on movement type
        if ($movementType === 'in') {
            $newQuantity = $currentStock + $quantity;
        } elseif ($movementType === 'out') {
            $newQuantity = max(0, $currentStock - $quantity);
        } else { // adjustment
            $newQuantity = $quantity;
        }
        
        // Update stock table
        $sql = "INSERT INTO stock (product_id, quantity) VALUES (?, ?) 
                ON DUPLICATE KEY UPDATE quantity = VALUES(quantity)";
        $result = $this->query($sql, [$productId, $newQuantity]);
        
        if ($result) {
            // Record stock movement
            $this->recordStockMovement($productId, $movementType, $quantity, $referenceType, $referenceId, $notes);
            return true;
        }
        
        return false;
    }
    
    /**
     * Get current stock quantity
     */
    public function getStockQuantity($productId) {
        $sql = "SELECT COALESCE(quantity, 0) as quantity FROM stock WHERE product_id = ?";
        $result = $this->query($sql, [$productId]);
        return $result ? $result->fetch_assoc()['quantity'] : 0;
    }
    
    /**
     * Record stock movement
     */
    private function recordStockMovement($productId, $movementType, $quantity, $referenceType, $referenceId = null, $notes = '') {
        $sql = "INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, notes, user_id) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $productId,
            $movementType,
            $quantity,
            $referenceType,
            $referenceId,
            $notes,
            Auth::getUserId()
        ];
        
        return $this->query($sql, $params) !== false;
    }
    
    /**
     * Get stock movements for a product
     */
    public function getStockMovements($productId, $limit = 50) {
        $sql = "SELECT sm.*, u.username as user_name 
                FROM stock_movements sm 
                LEFT JOIN users u ON sm.user_id = u.id 
                WHERE sm.product_id = ? 
                ORDER BY sm.created_at DESC 
                LIMIT ?";
        
        $result = $this->query($sql, [$productId, $limit]);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
    
    /**
     * Get low stock products
     */
    public function getLowStockProducts() {
        $sql = "SELECT p.*, c.name as category_name, 
                       COALESCE(s.quantity, 0) as stock_quantity,
                       p.min_stock_level
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                LEFT JOIN stock s ON p.id = s.product_id 
                WHERE p.is_active = 1 
                AND COALESCE(s.quantity, 0) <= p.min_stock_level 
                ORDER BY (COALESCE(s.quantity, 0) / p.min_stock_level) ASC";
        
        $result = $this->query($sql);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
    
    /**
     * Search products for autocomplete
     */
    public function searchProducts($term, $limit = 10) {
        $sql = "SELECT p.id, p.name, p.sku, p.selling_price,
                       COALESCE(s.quantity, 0) as stock_quantity
                FROM products p
                LEFT JOIN stock s ON p.id = s.product_id
                WHERE p.is_active = 1
                AND (p.name LIKE ? OR p.sku LIKE ?)
                ORDER BY p.name ASC
                LIMIT ?";

        $searchTerm = "%$term%";
        $result = $this->query($sql, [$searchTerm, $searchTerm, $limit]);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }

    /**
     * Search products (alias for searchProducts)
     */
    public function search($term, $limit = 10) {
        return $this->searchProducts($term, $limit);
    }
    
    /**
     * Get stock statistics
     */
    public function getStats() {
        $stats = [];
        
        // Total products
        $result = $this->query("SELECT COUNT(*) as total FROM products WHERE is_active = 1");
        $stats['total_products'] = $result ? $result->fetch_assoc()['total'] : 0;
        
        // Low stock products
        $result = $this->query("SELECT COUNT(*) as total FROM products p LEFT JOIN stock s ON p.id = s.product_id WHERE p.is_active = 1 AND COALESCE(s.quantity, 0) <= p.min_stock_level");
        $stats['low_stock_products'] = $result ? $result->fetch_assoc()['total'] : 0;
        
        // Out of stock products
        $result = $this->query("SELECT COUNT(*) as total FROM products p LEFT JOIN stock s ON p.id = s.product_id WHERE p.is_active = 1 AND COALESCE(s.quantity, 0) = 0");
        $stats['out_of_stock_products'] = $result ? $result->fetch_assoc()['total'] : 0;
        
        // Total stock value
        $result = $this->query("SELECT SUM(p.purchase_price * COALESCE(s.quantity, 0)) as total FROM products p LEFT JOIN stock s ON p.id = s.product_id WHERE p.is_active = 1");
        $stats['total_stock_value'] = $result ? ($result->fetch_assoc()['total'] ?? 0) : 0;
        
        return $stats;
    }
    
    /**
     * Check if SKU exists
     */
    public function skuExists($sku, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM products WHERE sku = ?";
        $params = [$sku];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->query($sql, $params);
        return $result ? $result->fetch_assoc()['count'] > 0 : false;
    }
    
    /**
     * Generate unique SKU
     */
    public function generateSku($productName) {
        $prefix = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $productName), 0, 3));
        $counter = 1;
        
        do {
            $sku = $prefix . str_pad($counter, 4, '0', STR_PAD_LEFT);
            $counter++;
        } while ($this->skuExists($sku));
        
        return $sku;
    }
}
