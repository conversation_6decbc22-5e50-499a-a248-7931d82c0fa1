<form method="POST" action="<?= $this->url('?page=login&action=attempt') ?>" class="auth-form">
    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
    
    <?php if (isset($timeout)): ?>
        <div class="alert alert-warning" role="alert">
            <i class="bi bi-exclamation-triangle"></i>
            <?= $this->escape($timeout) ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($error)): ?>
        <div class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-circle"></i>
            <?= $this->escape($error) ?>
        </div>
    <?php endif; ?>

    <?php if (isset($errors['login'])): ?>
        <div class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-circle"></i>
            <?= $this->escape($errors['login']) ?>
        </div>
    <?php endif; ?>
    
    <div class="mb-3">
        <label for="username" class="form-label">
            <i class="bi bi-person"></i>
            Username
        </label>
        <input 
            type="text" 
            class="form-control <?= isset($errors['username']) ? 'is-invalid' : '' ?>" 
            id="username" 
            name="username" 
            value="<?= $this->escape($old_username ?? '') ?>"
            placeholder="Enter your username"
            required
            autofocus
        >
        <?php if (isset($errors['username'])): ?>
            <div class="invalid-feedback">
                <?= $this->escape($errors['username']) ?>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="mb-3">
        <label for="password" class="form-label">
            <i class="bi bi-lock"></i>
            Password
        </label>
        <div class="input-group">
            <input 
                type="password" 
                class="form-control <?= isset($errors['password']) ? 'is-invalid' : '' ?>" 
                id="password" 
                name="password" 
                placeholder="Enter your password"
                required
            >
            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                <i class="bi bi-eye" id="togglePasswordIcon"></i>
            </button>
            <?php if (isset($errors['password'])): ?>
                <div class="invalid-feedback">
                    <?= $this->escape($errors['password']) ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="mb-3 form-check">
        <input type="checkbox" class="form-check-input" id="remember" name="remember">
        <label class="form-check-label" for="remember">
            Remember me
        </label>
    </div>
    
    <div class="d-grid">
        <button type="submit" class="btn btn-primary btn-lg">
            <i class="bi bi-box-arrow-in-right"></i>
            Login
        </button>
    </div>
</form>

<div class="auth-info mt-4">
    <div class="row text-center">
        <div class="col-12">
            <h6 class="text-muted mb-3">Default Login Credentials:</h6>
        </div>
        <div class="col-6">
            <div class="card bg-light">
                <div class="card-body py-2">
                    <small class="text-muted d-block">Admin</small>
                    <strong>admin / admin123</strong>
                </div>
            </div>
        </div>
        <div class="col-6">
            <div class="card bg-light">
                <div class="card-body py-2">
                    <small class="text-muted d-block">Staff</small>
                    <strong>staff / staff123</strong>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="auth-features mt-4">
    <h6 class="text-center text-muted mb-3">System Features</h6>
    <div class="row text-center">
        <div class="col-6 col-md-3">
            <i class="bi bi-receipt fs-4 text-primary"></i>
            <small class="d-block">Invoice Management</small>
        </div>
        <div class="col-6 col-md-3">
            <i class="bi bi-box-seam fs-4 text-success"></i>
            <small class="d-block">Stock Control</small>
        </div>
        <div class="col-6 col-md-3">
            <i class="bi bi-people fs-4 text-info"></i>
            <small class="d-block">Customer Management</small>
        </div>
        <div class="col-6 col-md-3">
            <i class="bi bi-graph-up fs-4 text-warning"></i>
            <small class="d-block">Reports & Analytics</small>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    const password = document.getElementById('password');
    const togglePasswordIcon = document.getElementById('togglePasswordIcon');
    
    togglePassword.addEventListener('click', function() {
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);
        
        if (type === 'password') {
            togglePasswordIcon.className = 'bi bi-eye';
        } else {
            togglePasswordIcon.className = 'bi bi-eye-slash';
        }
    });
    
    // Auto-focus on username field
    document.getElementById('username').focus();
});
</script>
