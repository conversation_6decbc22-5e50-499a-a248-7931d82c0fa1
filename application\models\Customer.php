<?php
/**
 * Customer Model
 * Handles customer-related database operations
 */

class Customer extends Model {
    protected $table = 'customers';
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * Get all customers with pagination
     */
    public function getAll($page = 1, $limit = RECORDS_PER_PAGE, $search = '') {
        $offset = ($page - 1) * $limit;
        
        $whereClause = "WHERE is_active = 1";
        $params = [];
        
        if (!empty($search)) {
            $whereClause .= " AND (name LIKE ? OR email LIKE ? OR phone LIKE ? OR city LIKE ?)";
            $searchTerm = "%$search%";
            $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
        }
        
        $sql = "SELECT * FROM customers $whereClause ORDER BY name ASC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $result = $this->query($sql, $params);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
    
    /**
     * Get total customer count
     */
    public function getTotalCount($search = '') {
        $whereClause = "WHERE is_active = 1";
        $params = [];
        
        if (!empty($search)) {
            $whereClause .= " AND (name LIKE ? OR email LIKE ? OR phone LIKE ? OR city LIKE ?)";
            $searchTerm = "%$search%";
            $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
        }
        
        $sql = "SELECT COUNT(*) as total FROM customers $whereClause";
        $result = $this->query($sql, $params);
        return $result ? $result->fetch_assoc()['total'] : 0;
    }
    
    /**
     * Find customer by ID
     */
    public function findById($id) {
        $sql = "SELECT * FROM customers WHERE id = ? AND is_active = 1";
        $result = $this->query($sql, [$id]);
        return $result ? $result->fetch_assoc() : null;
    }
    
    /**
     * Find customer by email
     */
    public function findByEmail($email) {
        $sql = "SELECT * FROM customers WHERE email = ? AND is_active = 1";
        $result = $this->query($sql, [$email]);
        return $result ? $result->fetch_assoc() : null;
    }
    
    /**
     * Find customer by phone
     */
    public function findByPhone($phone) {
        $sql = "SELECT * FROM customers WHERE phone = ? AND is_active = 1";
        $result = $this->query($sql, [$phone]);
        return $result ? $result->fetch_assoc() : null;
    }
    
    /**
     * Create new customer
     */
    public function create($data) {
        $sql = "INSERT INTO customers (name, email, phone, address, city, state, pincode, gstin) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $data['name'],
            $data['email'] ?? null,
            $data['phone'] ?? null,
            $data['address'] ?? null,
            $data['city'] ?? null,
            $data['state'] ?? null,
            $data['pincode'] ?? null,
            $data['gstin'] ?? null
        ];
        
        $result = $this->query($sql, $params);
        return $result ? $this->db->insert_id : false;
    }
    
    /**
     * Update customer
     */
    public function update($id, $data) {
        $setParts = [];
        $params = [];
        
        $allowedFields = ['name', 'email', 'phone', 'address', 'city', 'state', 'pincode', 'gstin', 'is_active'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $setParts[] = "$field = ?";
                $params[] = $data[$field];
            }
        }
        
        if (empty($setParts)) {
            return false;
        }
        
        $params[] = $id;
        $sql = "UPDATE customers SET " . implode(', ', $setParts) . " WHERE id = ?";
        
        return $this->query($sql, $params) !== false;
    }
    
    /**
     * Delete customer (soft delete)
     */
    public function delete($id) {
        $sql = "UPDATE customers SET is_active = 0 WHERE id = ?";
        return $this->query($sql, [$id]) !== false;
    }
    
    /**
     * Search customers for autocomplete
     */
    public function searchCustomers($term, $limit = 10) {
        $sql = "SELECT id, name, email, phone, city 
                FROM customers 
                WHERE is_active = 1 
                AND (name LIKE ? OR email LIKE ? OR phone LIKE ?) 
                ORDER BY name ASC 
                LIMIT ?";
        
        $searchTerm = "%$term%";
        $result = $this->query($sql, [$searchTerm, $searchTerm, $searchTerm, $limit]);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
    
    /**
     * Get customer with invoice statistics
     */
    public function getCustomerWithStats($id) {
        $customer = $this->findById($id);
        if (!$customer) {
            return null;
        }
        
        // Get invoice statistics
        $sql = "SELECT 
                    COUNT(*) as total_invoices,
                    SUM(total_amount) as total_amount,
                    SUM(paid_amount) as paid_amount,
                    SUM(total_amount - paid_amount) as outstanding_amount,
                    MAX(invoice_date) as last_invoice_date
                FROM invoices 
                WHERE customer_id = ?";
        
        $result = $this->query($sql, [$id]);
        $stats = $result ? $result->fetch_assoc() : [];
        
        return array_merge($customer, [
            'total_invoices' => $stats['total_invoices'] ?? 0,
            'total_amount' => $stats['total_amount'] ?? 0,
            'paid_amount' => $stats['paid_amount'] ?? 0,
            'outstanding_amount' => $stats['outstanding_amount'] ?? 0,
            'last_invoice_date' => $stats['last_invoice_date'] ?? null
        ]);
    }
    
    /**
     * Get customer invoices
     */
    public function getCustomerInvoices($customerId, $limit = 10) {
        $sql = "SELECT * FROM invoices 
                WHERE customer_id = ? 
                ORDER BY invoice_date DESC 
                LIMIT ?";
        
        $result = $this->query($sql, [$customerId, $limit]);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
    
    /**
     * Get customers with outstanding amounts
     */
    public function getCustomersWithOutstanding() {
        $sql = "SELECT c.*, 
                       SUM(i.total_amount - i.paid_amount) as outstanding_amount,
                       COUNT(i.id) as pending_invoices
                FROM customers c 
                INNER JOIN invoices i ON c.id = i.customer_id 
                WHERE c.is_active = 1 
                AND i.status != 'paid' 
                AND (i.total_amount - i.paid_amount) > 0
                GROUP BY c.id 
                ORDER BY outstanding_amount DESC";
        
        $result = $this->query($sql);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
    
    /**
     * Get top customers by revenue
     */
    public function getTopCustomers($limit = 10) {
        $sql = "SELECT c.*, 
                       SUM(i.total_amount) as total_revenue,
                       COUNT(i.id) as total_invoices
                FROM customers c 
                INNER JOIN invoices i ON c.id = i.customer_id 
                WHERE c.is_active = 1 
                AND i.status = 'paid'
                GROUP BY c.id 
                ORDER BY total_revenue DESC 
                LIMIT ?";
        
        $result = $this->query($sql, [$limit]);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
    
    /**
     * Check if email exists
     */
    public function emailExists($email, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM customers WHERE email = ?";
        $params = [$email];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->query($sql, $params);
        return $result ? $result->fetch_assoc()['count'] > 0 : false;
    }
    
    /**
     * Check if phone exists
     */
    public function phoneExists($phone, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM customers WHERE phone = ?";
        $params = [$phone];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->query($sql, $params);
        return $result ? $result->fetch_assoc()['count'] > 0 : false;
    }
    
    /**
     * Get customer statistics
     */
    public function getStats() {
        $stats = [];
        
        // Total customers
        $result = $this->query("SELECT COUNT(*) as total FROM customers WHERE is_active = 1");
        $stats['total_customers'] = $result ? $result->fetch_assoc()['total'] : 0;
        
        // New customers this month
        $result = $this->query("SELECT COUNT(*) as total FROM customers WHERE is_active = 1 AND MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())");
        $stats['new_customers_this_month'] = $result ? $result->fetch_assoc()['total'] : 0;
        
        // Customers with outstanding amounts
        $result = $this->query("SELECT COUNT(DISTINCT c.id) as total FROM customers c INNER JOIN invoices i ON c.id = i.customer_id WHERE c.is_active = 1 AND i.status != 'paid' AND (i.total_amount - i.paid_amount) > 0");
        $stats['customers_with_outstanding'] = $result ? $result->fetch_assoc()['total'] : 0;
        
        // Total outstanding amount
        $result = $this->query("SELECT SUM(i.total_amount - i.paid_amount) as total FROM customers c INNER JOIN invoices i ON c.id = i.customer_id WHERE c.is_active = 1 AND i.status != 'paid'");
        $stats['total_outstanding'] = $result ? ($result->fetch_assoc()['total'] ?? 0) : 0;
        
        return $stats;
    }
    
    /**
     * Get customers by city
     */
    public function getCustomersByCity() {
        $sql = "SELECT city, COUNT(*) as customer_count 
                FROM customers 
                WHERE is_active = 1 AND city IS NOT NULL AND city != '' 
                GROUP BY city 
                ORDER BY customer_count DESC";
        
        $result = $this->query($sql);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
    
    /**
     * Get customers by state
     */
    public function getCustomersByState() {
        $sql = "SELECT state, COUNT(*) as customer_count 
                FROM customers 
                WHERE is_active = 1 AND state IS NOT NULL AND state != '' 
                GROUP BY state 
                ORDER BY customer_count DESC";
        
        $result = $this->query($sql);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
}
