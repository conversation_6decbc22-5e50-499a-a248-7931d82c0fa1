<?php
/**
 * API Controller
 * Handles AJAX/API requests
 */

class ApiController extends Controller {
    
    public function __construct() {
        parent::__construct();
        
        // Ensure user is logged in for all API calls
        if (!Auth::isLoggedIn()) {
            $this->renderJson(['error' => 'Unauthorized'], 401);
        }
    }
    
    /**
     * Dashboard statistics API
     */
    public function dashboardStats() {
        if (!$this->isAjax()) {
            $this->renderJson(['error' => 'Invalid request'], 400);
        }
        
        $chart = $_GET['chart'] ?? '';
        $period = $_GET['period'] ?? 'monthly';
        
        try {
            switch ($chart) {
                case 'sales':
                    $data = $this->getSalesChartData($period);
                    break;
                    
                default:
                    // Return general dashboard stats
                    $data = $this->getDashboardStats();
                    break;
            }
            
            $this->renderJson(['success' => true, 'data' => $data]);
            
        } catch (Exception $e) {
            $this->renderJson(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * Search stock products
     */
    public function searchStock() {
        if (!$this->isAjax()) {
            $this->renderJson(['error' => 'Invalid request'], 400);
        }
        
        $query = $_GET['q'] ?? '';
        
        if (strlen($query) < 2) {
            $this->renderJson(['success' => true, 'data' => []]);
            return;
        }
        
        try {
            $stockModel = new Stock();
            $products = $stockModel->search($query, 10); // Limit to 10 results
            
            $this->renderJson(['success' => true, 'data' => $products]);
            
        } catch (Exception $e) {
            $this->renderJson(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * Search customers
     */
    public function searchCustomers() {
        if (!$this->isAjax()) {
            $this->renderJson(['error' => 'Invalid request'], 400);
        }
        
        $query = $_GET['q'] ?? '';
        
        if (strlen($query) < 2) {
            $this->renderJson(['success' => true, 'data' => []]);
            return;
        }
        
        try {
            $customerModel = new Customer();
            $customers = $customerModel->search($query, 10); // Limit to 10 results
            
            $this->renderJson(['success' => true, 'data' => $customers]);
            
        } catch (Exception $e) {
            $this->renderJson(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * Get sales chart data
     */
    private function getSalesChartData($period) {
        $cacheKey = "sales_chart_{$period}_" . date('Y-m-d');
        
        return $this->cache->remember($cacheKey, function() use ($period) {
            $invoiceModel = new Invoice();
            
            switch ($period) {
                case 'daily':
                    $sql = "SELECT 
                                DATE(invoice_date) as period,
                                SUM(total_amount) as total_sales,
                                COUNT(*) as invoice_count
                            FROM invoices 
                            WHERE invoice_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                            AND status = 'paid'
                            GROUP BY DATE(invoice_date)
                            ORDER BY period ASC";
                    break;
                    
                case 'weekly':
                    $sql = "SELECT 
                                YEARWEEK(invoice_date) as period,
                                SUM(total_amount) as total_sales,
                                COUNT(*) as invoice_count
                            FROM invoices 
                            WHERE invoice_date >= DATE_SUB(CURDATE(), INTERVAL 12 WEEK)
                            AND status = 'paid'
                            GROUP BY YEARWEEK(invoice_date)
                            ORDER BY period ASC";
                    break;
                    
                default: // monthly
                    $sql = "SELECT 
                                DATE_FORMAT(invoice_date, '%Y-%m') as period,
                                SUM(total_amount) as total_sales,
                                COUNT(*) as invoice_count
                            FROM invoices 
                            WHERE invoice_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                            AND status = 'paid'
                            GROUP BY DATE_FORMAT(invoice_date, '%Y-%m')
                            ORDER BY period ASC";
                    break;
            }
            
            $result = $invoiceModel->query($sql);
            return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
        }, 3600); // Cache for 1 hour
    }
    
    /**
     * Get dashboard statistics
     */
    private function getDashboardStats() {
        $cacheKey = 'dashboard_stats_' . date('Y-m-d-H');
        
        return $this->cache->remember($cacheKey, function() {
            try {
                $invoiceModel = new Invoice();
                $stockModel = new Stock();
                $customerModel = new Customer();
                $userModel = new User();
                
                $invoiceStats = $invoiceModel->getStats() ?: [];
                $stockStats = $stockModel->getStats() ?: [];
                $customerStats = $customerModel->getStats() ?: [];
                $userStats = $userModel->getStats() ?: [];
                
                return [
                    // Invoice Statistics
                    'total_invoices' => $invoiceStats['total_invoices'] ?? 0,
                    'draft_invoices' => $invoiceStats['draft_invoices'] ?? 0,
                    'paid_invoices' => $invoiceStats['paid_invoices'] ?? 0,
                    'overdue_invoices' => $invoiceStats['overdue_invoices'] ?? 0,
                    'total_revenue' => $invoiceStats['total_revenue'] ?? 0,
                    'pending_amount' => $invoiceStats['pending_amount'] ?? 0,
                    'monthly_revenue' => $invoiceStats['monthly_revenue'] ?? 0,
                    
                    // Stock Statistics
                    'total_products' => $stockStats['total_products'] ?? 0,
                    'low_stock_products' => $stockStats['low_stock_products'] ?? 0,
                    'out_of_stock_products' => $stockStats['out_of_stock_products'] ?? 0,
                    'total_stock_value' => $stockStats['total_stock_value'] ?? 0,
                    
                    // Customer Statistics
                    'total_customers' => $customerStats['total_customers'] ?? 0,
                    'new_customers_this_month' => $customerStats['new_customers_this_month'] ?? 0,
                    'customers_with_outstanding' => $customerStats['customers_with_outstanding'] ?? 0,
                    'total_outstanding' => $customerStats['total_outstanding'] ?? 0,
                    
                    // User Statistics
                    'total_users' => $userStats['total_users'] ?? 0,
                    'admin_count' => $userStats['admin_count'] ?? 0,
                    'staff_count' => $userStats['staff_count'] ?? 0
                ];
            } catch (Exception $e) {
                // Return default values if there's an error
                return [
                    'total_invoices' => 0, 'draft_invoices' => 0, 'paid_invoices' => 0, 'overdue_invoices' => 0,
                    'total_revenue' => 0, 'pending_amount' => 0, 'monthly_revenue' => 0,
                    'total_products' => 0, 'low_stock_products' => 0, 'out_of_stock_products' => 0, 'total_stock_value' => 0,
                    'total_customers' => 0, 'new_customers_this_month' => 0, 'customers_with_outstanding' => 0, 'total_outstanding' => 0,
                    'total_users' => 0, 'admin_count' => 0, 'staff_count' => 0
                ];
            }
        }, 3600); // Cache for 1 hour
    }
}
