<?php
/**
 * Main Configuration File
 * Invoice and Stock Management System
 */

// Environment Detection
function detectEnvironment() {
    $serverName = $_SERVER['SERVER_NAME'] ?? '';
    $httpHost = $_SERVER['HTTP_HOST'] ?? '';
    
    // Check if running on XAMPP/local environment
    if (strpos($serverName, 'localhost') !== false || 
        strpos($httpHost, 'localhost') !== false ||
        strpos($httpHost, '127.0.0.1') !== false ||
        strpos($httpHost, '::1') !== false) {
        return 'local';
    }
    
    return 'production';
}

// Set environment
define('ENVIRONMENT', detectEnvironment());

// Base configuration
if (ENVIRONMENT === 'local') {
    // Local/XAMPP Configuration
    define('BASE_URL', 'http://localhost/projects/gym/');
    define('DB_HOST', 'localhost');
    define('DB_USER', 'root');
    define('DB_PASS', '');
    define('DB_NAME', 'gym_invoice_system');
    define('DEBUG_MODE', true);
} else {
    // Production Configuration
    define('BASE_URL', 'https://yourdomain.com/');
    define('DB_HOST', 'localhost');
    define('DB_USER', 'your_db_user');
    define('DB_PASS', 'your_db_password');
    define('DB_NAME', 'your_db_name');
    define('DEBUG_MODE', false);
}

// Application Settings
define('APP_NAME', 'Invoice & Stock Management');
define('APP_VERSION', '1.0.0');
define('APP_AUTHOR', 'Your Company');

// Security Settings
define('SESSION_TIMEOUT', 3600); // 1 hour
define('PASSWORD_MIN_LENGTH', 6);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// File Upload Settings
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']);

// Cache Settings
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // 1 hour
define('CACHE_PATH', 'cache/');

// Indian Localization
define('TIMEZONE', 'Asia/Kolkata');
define('CURRENCY_SYMBOL', '₹');
define('CURRENCY_CODE', 'INR');
define('DATE_FORMAT', 'd/m/Y');
define('DATETIME_FORMAT', 'd/m/Y H:i:s');

// Email Configuration (for invoice sending)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_ENCRYPTION', 'tls');

// Pagination Settings
define('RECORDS_PER_PAGE', 10);

// Invoice Settings
define('INVOICE_PREFIX', 'INV');
define('INVOICE_NUMBER_LENGTH', 6);
define('TAX_RATE', 18); // GST rate in percentage

// Stock Settings
define('LOW_STOCK_THRESHOLD', 10);
define('STOCK_ALERT_ENABLED', true);

// System Paths
define('VIEWS_PATH', 'application/views/');
define('MODELS_PATH', 'application/models/');
define('CONTROLLERS_PATH', 'application/controllers/');
define('ASSETS_PATH', 'assets/');
define('UPLOADS_PATH', 'uploads/');

// Create necessary directories if they don't exist
$directories = [
    CACHE_PATH,
    UPLOADS_PATH,
    UPLOADS_PATH . 'invoices/',
    UPLOADS_PATH . 'products/',
    'logs/'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Helper Functions
function formatCurrency($amount) {
    return CURRENCY_SYMBOL . ' ' . number_format($amount, 2);
}

function formatDate($date, $format = DATE_FORMAT) {
    return date($format, strtotime($date));
}

function formatDateTime($datetime, $format = DATETIME_FORMAT) {
    return date($format, strtotime($datetime));
}

function generateInvoiceNumber() {
    $prefix = INVOICE_PREFIX;
    $length = INVOICE_NUMBER_LENGTH;
    $number = str_pad(rand(1, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    return $prefix . $number;
}

function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

function redirectTo($url) {
    header('Location: ' . BASE_URL . $url);
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function hasRole($role) {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === $role;
}

function isAdmin() {
    return hasRole('admin');
}

function isStaff() {
    return hasRole('staff');
}

function logActivity($action, $details = '') {
    $logFile = 'logs/activity_' . date('Y-m-d') . '.log';
    $timestamp = date(DATETIME_FORMAT);
    $userId = $_SESSION['user_id'] ?? 'Guest';
    $userRole = $_SESSION['user_role'] ?? 'Unknown';
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    
    $logEntry = "[$timestamp] User: $userId ($userRole) | IP: $ip | Action: $action | Details: $details" . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// Set error reporting based on environment
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', 'logs/error_' . date('Y-m-d') . '.log');
}
