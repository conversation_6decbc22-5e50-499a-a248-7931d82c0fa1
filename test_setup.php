<?php
/**
 * Test Setup Script
 * Verify database connection and basic functionality
 */

// Include configuration
require_once 'config/config.php';

echo "<h1>Invoice & Stock Management System - Setup Test</h1>";
echo "<hr>";

// Test 1: Configuration
echo "<h3>1. Configuration Test</h3>";
echo "Environment: " . ENVIRONMENT . "<br>";
echo "Base URL: " . BASE_URL . "<br>";
echo "Database Host: " . DB_HOST . "<br>";
echo "Database Name: " . DB_NAME . "<br>";
echo "Timezone: " . TIMEZONE . "<br>";
echo "Currency: " . CURRENCY_SYMBOL . "<br>";
echo "<span style='color: green;'>✓ Configuration loaded successfully</span><br><br>";

// Test 2: Database Connection
echo "<h3>2. Database Connection Test</h3>";
try {
    $db = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($db->connect_errno) {
        echo "<span style='color: red;'>✗ Database connection failed: " . $db->connect_error . "</span><br>";
    } else {
        echo "<span style='color: green;'>✓ Database connection successful</span><br>";
        
        // Test database setup
        echo "<h3>3. Database Setup Test</h3>";
        include 'application/database_setup.php';
        echo "<br>";
        
        // Test 4: Check tables
        echo "<h3>4. Database Tables Test</h3>";
        $tables = ['users', 'customers', 'categories', 'products', 'stock', 'invoices', 'invoice_items', 'payments', 'settings', 'activity_logs', 'stock_movements'];
        
        foreach ($tables as $table) {
            $result = $db->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                echo "<span style='color: green;'>✓ Table '$table' exists</span><br>";
            } else {
                echo "<span style='color: red;'>✗ Table '$table' missing</span><br>";
            }
        }
        
        // Test 5: Check default data
        echo "<br><h3>5. Default Data Test</h3>";
        
        // Check admin user
        $result = $db->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
        $adminCount = $result->fetch_assoc()['count'];
        if ($adminCount > 0) {
            echo "<span style='color: green;'>✓ Admin user exists ($adminCount)</span><br>";
        } else {
            echo "<span style='color: red;'>✗ No admin user found</span><br>";
        }
        
        // Check categories
        $result = $db->query("SELECT COUNT(*) as count FROM categories");
        $categoryCount = $result->fetch_assoc()['count'];
        if ($categoryCount > 0) {
            echo "<span style='color: green;'>✓ Categories exist ($categoryCount)</span><br>";
        } else {
            echo "<span style='color: red;'>✗ No categories found</span><br>";
        }
        
        // Check settings
        $result = $db->query("SELECT COUNT(*) as count FROM settings");
        $settingsCount = $result->fetch_assoc()['count'];
        if ($settingsCount > 0) {
            echo "<span style='color: green;'>✓ Settings exist ($settingsCount)</span><br>";
        } else {
            echo "<span style='color: red;'>✗ No settings found</span><br>";
        }
        
        $db->close();
    }
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ Database error: " . $e->getMessage() . "</span><br>";
}

// Test 6: File Permissions
echo "<br><h3>6. File Permissions Test</h3>";
$directories = ['cache/', 'uploads/', 'logs/'];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<span style='color: green;'>✓ Directory '$dir' is writable</span><br>";
        } else {
            echo "<span style='color: orange;'>⚠ Directory '$dir' is not writable</span><br>";
        }
    } else {
        echo "<span style='color: red;'>✗ Directory '$dir' does not exist</span><br>";
    }
}

// Test 7: PHP Extensions
echo "<br><h3>7. PHP Extensions Test</h3>";
$required_extensions = ['mysqli', 'json', 'session', 'mbstring'];

foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<span style='color: green;'>✓ Extension '$ext' is loaded</span><br>";
    } else {
        echo "<span style='color: red;'>✗ Extension '$ext' is missing</span><br>";
    }
}

// Test 8: Core Classes
echo "<br><h3>8. Core Classes Test</h3>";
$core_files = [
    'application/core/Model.php',
    'application/core/View.php',
    'application/core/Controller.php',
    'application/core/Router.php',
    'application/core/Auth.php',
    'application/core/Cache.php'
];

foreach ($core_files as $file) {
    if (file_exists($file)) {
        echo "<span style='color: green;'>✓ File '$file' exists</span><br>";
    } else {
        echo "<span style='color: red;'>✗ File '$file' missing</span><br>";
    }
}

echo "<br><h3>Setup Test Complete!</h3>";
echo "<p>If all tests show green checkmarks, your system is ready to use.</p>";
echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Application</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}

h1 {
    color: #333;
    text-align: center;
}

h3 {
    color: #555;
    border-bottom: 2px solid #ddd;
    padding-bottom: 5px;
}

hr {
    border: none;
    border-top: 2px solid #ddd;
    margin: 20px 0;
}
</style>
