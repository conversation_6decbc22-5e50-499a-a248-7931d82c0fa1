# Invoice & Stock Management System

A comprehensive web-based invoice and stock management system built with PHP, featuring a responsive Bootstrap interface, role-based access control, and advanced caching for optimal performance.

## 🚀 Features

### Core Functionality
- **Invoice Management**: Create, edit, view, and manage invoices with PDF generation
- **Stock Management**: Track products, manage inventory, low stock alerts
- **Customer Management**: Maintain customer database with purchase history
- **User Management**: Admin and staff roles with different access levels
- **Dashboard**: Real-time statistics and analytics with interactive charts
- **Reports**: Comprehensive sales and stock reports with export functionality

### Technical Features
- **MVC Architecture**: Clean, organized code structure
- **Responsive Design**: Mobile-first Bootstrap 5 interface
- **Caching System**: File-based caching for improved performance
- **Environment Detection**: Automatic XAMPP/live server detection
- **Database Management**: Auto-creation and migration system
- **Security**: CSRF protection, password hashing, session management
- **Indian Localization**: INR currency, Indian timezone, GST support

## 📋 Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- PHP Extensions: mysqli, json, session, mbstring

## 🛠️ Installation

### XAMPP Installation (Local Development)

1. **Download and Install XAMPP**
   - Download from [https://www.apachefriends.org/](https://www.apachefriends.org/)
   - Install and start Apache and MySQL services

2. **Clone/Download the Project**
   ```bash
   cd C:\xampp\htdocs\projects\
   # Clone or extract the project files to 'gym' folder
   ```

3. **Run Setup Test**
   - Open browser and navigate to: `http://localhost/projects/gym/test_setup.php`
   - Verify all tests pass (green checkmarks)

4. **Access the Application**
   - Navigate to: `http://localhost/projects/gym/`
   - Use default credentials to login

### Live Server Installation

1. **Upload Files**
   - Upload all files to your web server's public directory
   - Ensure proper file permissions (755 for directories, 644 for files)

2. **Configure Database**
   - Create a MySQL database
   - Update database credentials in `config/config.php`

3. **Set Permissions**
   ```bash
   chmod 755 cache/ uploads/ logs/
   chmod 644 config/config.php
   ```

4. **Run Setup**
   - Navigate to: `https://yourdomain.com/test_setup.php`
   - Verify installation and remove test file

## 🔐 Default Login Credentials

### Administrator Account
- **Username**: `admin`
- **Password**: `admin123`
- **Access**: Full system access

### Staff Account
- **Username**: `staff`
- **Password**: `staff123`
- **Access**: Limited access (no user management/settings)

> **Important**: Change default passwords after first login!

## 📁 Project Structure

```
gym/
├── application/
│   ├── controllers/          # Application controllers
│   ├── core/                # Core framework files
│   ├── models/              # Database models
│   ├── views/               # View templates
│   └── database_setup.php   # Database initialization
├── assets/
│   ├── css/                 # Stylesheets
│   ├── js/                  # JavaScript files
│   └── img/                 # Images
├── cache/                   # Cache files (auto-created)
├── config/
│   └── config.php           # Main configuration
├── logs/                    # Log files (auto-created)
├── uploads/                 # File uploads (auto-created)
├── index.php               # Main entry point
├── test_setup.php          # Setup verification
└── README.md               # This file
```

## 🎯 Usage Guide

### Dashboard
- View real-time statistics and KPIs
- Monitor recent invoices and low stock alerts
- Interactive sales charts with different time periods
- Quick access to all major functions

### Invoice Management
1. **Create Invoice**: Add customer, select products, set terms
2. **Manage Status**: Draft → Sent → Paid workflow
3. **Generate PDF**: Professional invoice templates
4. **Track Payments**: Record partial and full payments

### Stock Management
1. **Add Products**: Create products with categories and pricing
2. **Manage Inventory**: Track stock levels and movements
3. **Low Stock Alerts**: Automatic notifications for reorder
4. **Stock Reports**: Detailed inventory analysis

### Customer Management
1. **Customer Database**: Store contact and billing information
2. **Purchase History**: Track customer transactions
3. **Outstanding Amounts**: Monitor pending payments

## ⚙️ Configuration

### Environment Settings
The system automatically detects if running on XAMPP or live server. Manual configuration available in `config/config.php`:

```php
// Force environment (optional)
define('ENVIRONMENT', 'local'); // or 'production'

// Database settings
define('DB_HOST', 'localhost');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_NAME', 'your_database');
```

### Application Settings
- **Company Information**: Update in Settings panel
- **Tax Rates**: Configure GST/tax percentages
- **Currency**: Indian Rupee (₹) by default
- **Timezone**: Asia/Kolkata by default

## 🔧 Advanced Features

### Caching System
- File-based caching for improved performance
- Automatic cache invalidation
- Admin cache management tools

### Security Features
- CSRF token protection
- Password hashing (bcrypt)
- Session timeout management
- Role-based access control
- SQL injection prevention

### API Endpoints
- RESTful API for mobile integration
- JSON responses for AJAX calls
- Search and autocomplete functionality

## 📊 Database Schema

### Core Tables
- `users` - User accounts and roles
- `customers` - Customer information
- `products` - Product catalog
- `categories` - Product categories
- `stock` - Inventory levels
- `invoices` - Invoice headers
- `invoice_items` - Invoice line items
- `payments` - Payment records
- `settings` - Application settings
- `activity_logs` - User activity tracking

## 🚀 Performance Optimization

### Caching Strategy
- Dashboard statistics cached for 1 hour
- Product searches cached for 30 minutes
- User data cached for 5 minutes

### Database Optimization
- Proper indexing on frequently queried columns
- Optimized queries with joins
- Pagination for large datasets

## 🔍 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Verify MySQL service is running
   - Check database credentials in config
   - Ensure database exists

2. **Permission Errors**
   - Set proper file permissions (755/644)
   - Ensure web server can write to cache/uploads/logs

3. **Session Issues**
   - Check PHP session configuration
   - Verify session directory permissions

4. **Cache Problems**
   - Clear cache directory manually
   - Check cache directory permissions

### Debug Mode
Enable debug mode in `config/config.php`:
```php
define('DEBUG_MODE', true);
```

## 📈 Future Enhancements

- **Mobile App**: React Native mobile application
- **Email Integration**: Automated invoice sending
- **Barcode Support**: Product scanning functionality
- **Multi-language**: Hindi and English support
- **Advanced Reports**: Profit/loss, tax reports
- **Backup System**: Automated database backups

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

## 🙏 Acknowledgments

- Bootstrap team for the responsive framework
- Chart.js for interactive charts
- PHP community for excellent documentation
- All contributors and testers

---

**Version**: 1.0.0  
**Last Updated**: <?= date('Y-m-d') ?>  
**Author**: Your Company
