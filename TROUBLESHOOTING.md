# Troubleshooting Guide
## Invoice & Stock Management System

### Common Issues and Solutions

#### 1. "Too Many Redirects" Error (ERR_TOO_MANY_REDIRECTS)

**Symptoms:**
- <PERSON>rowser shows "This page isn't working" or "Too many redirects"
- <PERSON> keeps loading indefinitely

**Solutions:**
1. **Clear Browser Data:**
   - Clear cookies and site data for localhost
   - Use Ctrl+Shift+Delete in Chrome/Firefox
   - Or use incognito/private browsing mode

2. **Clear Server Sessions:**
   - Visit: `http://localhost/projects/gym/clear_session.php`
   - This will clear all session data

3. **Check Authentication Status:**
   - Visit: `http://localhost/projects/gym/debug.php`
   - Check if session data is corrupted

#### 2. "Page Not Found" Error (404)

**Symptoms:**
- Apache shows "The requested URL was not found"
- 404 error page

**Solutions:**
1. **Check XAMPP Status:**
   - Ensure Apache and MySQL are running in XAMPP Control Panel
   - Green indicators should be visible

2. **Verify File Location:**
   - Files should be in: `C:\xampp\htdocs\projects\gym\`
   - Check if `index.php` exists in the directory

3. **Test Direct Access:**
   - Try: `http://localhost/projects/gym/index.php`
   - Try: `http://localhost/projects/gym/simple_test.php`

#### 3. Database Connection Errors

**Symptoms:**
- "Database connection failed" messages
- MySQL errors

**Solutions:**
1. **Check MySQL Service:**
   - Start MySQL in XAMPP Control Panel
   - Ensure port 3306 is not blocked

2. **Verify Database:**
   - Run: `http://localhost/projects/gym/test_setup.php`
   - Check if database `gym_invoice_system` exists

3. **Manual Database Creation:**
   ```sql
   CREATE DATABASE gym_invoice_system;
   ```

#### 4. PHP Errors

**Symptoms:**
- White screen (WSOD)
- PHP error messages
- Fatal errors

**Solutions:**
1. **Check PHP Version:**
   - Visit: `http://localhost/projects/gym/phpinfo.php`
   - Ensure PHP 7.4+ is running

2. **Enable Error Display:**
   - Edit `config/config.php`
   - Set `define('DEBUG_MODE', true);`

3. **Check Error Logs:**
   - Look in `logs/` directory for error files
   - Check XAMPP error logs

#### 5. Login Issues

**Symptoms:**
- Cannot login with default credentials
- "Invalid username or password" errors

**Solutions:**
1. **Use Default Credentials:**
   - Admin: `admin` / `admin123`
   - Staff: `staff` / `staff123`

2. **Reset Database:**
   - Delete database `gym_invoice_system`
   - Visit setup page to recreate

3. **Check User Table:**
   ```sql
   SELECT * FROM users;
   ```

#### 6. Permission Errors

**Symptoms:**
- Cannot write to cache/logs/uploads
- File permission errors

**Solutions:**
1. **Set Directory Permissions:**
   ```bash
   chmod 755 cache/ uploads/ logs/
   ```

2. **Check Directory Existence:**
   - Ensure directories exist and are writable
   - XAMPP usually handles this automatically

#### 7. CSS/JS Not Loading

**Symptoms:**
- Page appears unstyled
- JavaScript features not working

**Solutions:**
1. **Check Asset Paths:**
   - Verify `assets/css/` and `assets/js/` directories exist
   - Check if files are accessible directly

2. **Clear Browser Cache:**
   - Hard refresh with Ctrl+F5
   - Clear browser cache completely

### Quick Diagnostic Steps

1. **Basic Connectivity:**
   ```
   http://localhost/projects/gym/phpinfo.php
   ```

2. **Database Test:**
   ```
   http://localhost/projects/gym/test_setup.php
   ```

3. **Simple Application Test:**
   ```
   http://localhost/projects/gym/simple_test.php
   ```

4. **Authentication Debug:**
   ```
   http://localhost/projects/gym/debug.php
   ```

5. **Clear Sessions:**
   ```
   http://localhost/projects/gym/clear_session.php
   ```

### Environment-Specific Issues

#### XAMPP on Windows
- Ensure no other web servers are running on port 80
- Check Windows Firewall settings
- Run XAMPP as Administrator if needed

#### Port Conflicts
- Change Apache port in XAMPP if 80 is occupied
- Update `BASE_URL` in `config/config.php` accordingly

### Advanced Troubleshooting

#### Enable Debug Mode
Edit `config/config.php`:
```php
define('DEBUG_MODE', true);
```

#### Check PHP Extensions
Required extensions:
- mysqli
- json
- session
- mbstring

#### Database Manual Setup
If auto-setup fails:
```sql
CREATE DATABASE gym_invoice_system;
USE gym_invoice_system;
-- Run SQL from application/database_setup.php manually
```

### Getting Help

1. **Check Error Logs:**
   - `logs/error_YYYY-MM-DD.log`
   - `logs/activity_YYYY-MM-DD.log`

2. **System Information:**
   - PHP version and extensions
   - MySQL version
   - Operating system

3. **Provide Details:**
   - Exact error messages
   - Steps to reproduce
   - Browser and version
   - XAMPP version

### Prevention Tips

1. **Regular Backups:**
   - Export database regularly
   - Backup configuration files

2. **Keep Updated:**
   - Update XAMPP regularly
   - Monitor PHP version compatibility

3. **Monitor Logs:**
   - Check error logs periodically
   - Monitor disk space for cache/logs

4. **Test After Changes:**
   - Always test after configuration changes
   - Use staging environment for major updates

### Emergency Recovery

If system is completely broken:

1. **Backup Data:**
   ```sql
   mysqldump -u root gym_invoice_system > backup.sql
   ```

2. **Fresh Installation:**
   - Delete all files except uploads/
   - Re-extract system files
   - Restore database from backup

3. **Restore Configuration:**
   - Update `config/config.php` with your settings
   - Test with `test_setup.php`

### Contact Information

For additional support:
- Check documentation in README.md
- Review code comments for specific functions
- Test with provided diagnostic tools
