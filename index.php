<?php
/**
 * Invoice and Stock Management System
 * Main Entry Point - Simplified Version
 */

// Start session
session_start();

// Error reporting configuration
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set Indian timezone
date_default_timezone_set('Asia/Kolkata');

// Include configuration
require_once 'config/config.php';

// Include core classes
require_once 'application/core/Model.php';
require_once 'application/core/View.php';
require_once 'application/core/Auth.php';
require_once 'application/core/Cache.php';

// Include database setup (only run once)
if (!file_exists('cache/db_setup_done.flag')) {
    require_once 'application/database_setup.php';
    file_put_contents('cache/db_setup_done.flag', date('Y-m-d H:i:s'));
}

// Auto-load models and controllers
spl_autoload_register(function ($class) {
    $paths = [
        'application/models/' . $class . '.php',
        'application/controllers/' . $class . '.php'
    ];

    foreach ($paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            return;
        }
    }
});

// Simple routing without complex middleware
$page = $_GET['page'] ?? '';
$action = $_GET['action'] ?? '';

// Handle logout first
if ($page === 'logout') {
    Auth::logout();
    header('Location: ' . BASE_URL . '?page=login');
    exit();
}

// Check authentication for protected pages
$protectedPages = ['dashboard', 'invoices', 'stock', 'customers', 'reports', 'users', 'settings'];
$isProtectedPage = in_array($page, $protectedPages) || in_array(explode('/', $page)[0], $protectedPages);

if ($isProtectedPage && !Auth::isLoggedIn()) {
    header('Location: ' . BASE_URL . '?page=login');
    exit();
}

// Handle login attempts
if ($page === 'login' && $_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'attempt') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';

    if ($username && $password) {
        $result = Auth::attempt($username, $password);
        if ($result['success']) {
            header('Location: ' . BASE_URL . '?page=dashboard');
            exit();
        } else {
            $_SESSION['login_error'] = $result['message'];
        }
    }
}

// Route to appropriate controller
try {
    switch ($page) {
        case '':
        case 'login':
            if (Auth::isLoggedIn()) {
                header('Location: ' . BASE_URL . '?page=dashboard');
                exit();
            }
            $controller = new LoginController();
            $controller->display();
            break;

        case 'dashboard':
            $controller = new DashboardController();
            $controller->index();
            break;

        default:
            // Show 404 for unknown pages
            header("HTTP/1.0 404 Not Found");
            include 'application/views/errors/404.php';
            break;
    }
} catch (Exception $e) {
    if (DEBUG_MODE) {
        echo "Error: " . $e->getMessage();
    } else {
        echo "An error occurred. Please try again.";
    }
}