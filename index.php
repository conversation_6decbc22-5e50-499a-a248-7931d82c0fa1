<?php
// Error reporting configuration
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define the base URL
define('BASE_URL', 'http://localhost/gym/');

// Include core classes
require_once 'application/core/Model.php';
require_once 'application/core/View.php';

// Include database setup
require_once 'application/database_setup.php';

// Include models
require_once 'application/models/User.php';

// Include controllers
require_once 'application/controllers/LoginController.php';

// Error handling function
function errorHandler($errno, $errstr, $errfile, $errline) {
    switch ($errno) {
        case E_WARNING:
        case E_NOTICE:
            // Log warnings and notices
            error_log("[$errno] $errstr in $errfile on line $errline");
            break;
        default:
            // For other errors, display a generic message
            echo "An error occurred. Please try again.";
            exit();
    }
}

// Set custom error handler
set_error_handler('errorHandler');

// Get the requested page from the query parameter
$page = isset($_GET['page']) ? $_GET['page'] : 'login';

// Define routes
$routes = [
    'login' => 'LoginController@display',
    'login/attempt' => 'LoginController@attempt',
    // Add more routes as needed
];

// Handle the request
if (isset($routes[$page])) {
    list($controller, $method) = explode('@', $routes[$page]);
    $class = new $controller();
    $class->$method();
} else {
    // Handle 404 error
    header("HTTP/1.0 404 Not Found");
    echo "Page not found!";
}