<?php
/**
 * Invoice and Stock Management System
 * Main Entry Point
 */

// Start session
session_start();

// Error reporting configuration
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set Indian timezone
date_default_timezone_set('Asia/Kolkata');

// Include configuration
require_once 'config/config.php';

// Include core classes
require_once 'application/core/Model.php';
require_once 'application/core/View.php';
require_once 'application/core/Controller.php';
require_once 'application/core/Router.php';
require_once 'application/core/Auth.php';
require_once 'application/core/Cache.php';

// Include database setup
require_once 'application/database_setup.php';

// Auto-load models
spl_autoload_register(function ($class) {
    $modelPath = 'application/models/' . $class . '.php';
    $controllerPath = 'application/controllers/' . $class . '.php';

    if (file_exists($modelPath)) {
        require_once $modelPath;
    } elseif (file_exists($controllerPath)) {
        require_once $controllerPath;
    }
});

// Error handling function
function errorHandler($errno, $errstr, $errfile, $errline) {
    switch ($errno) {
        case E_WARNING:
        case E_NOTICE:
            // Log warnings and notices
            error_log("[$errno] $errstr in $errfile on line $errline");
            break;
        default:
            // For other errors, display a generic message
            echo "An error occurred. Please try again.";
            exit();
    }
}

// Set custom error handler
set_error_handler('errorHandler');

// Initialize router and handle request
$router = new Router();
$router->handleRequest();