<?php
class Model {
    protected $db;
    
    public function __construct() {
        $this->db = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        if ($this->db->connect_errno) {
            die("Failed to connect to MySQL: " . $this->db->connect_error);
        }
    }
    
    public function query($sql, $params = []) {
        $stmt = $this->db->prepare($sql);
        if ($stmt) {
            $this->bindParams($stmt, $params);
            $stmt->execute();
            return $stmt->get_result();
        }
        return false;
    }
    
    private function bindParams($stmt, $params) {
        if (empty($params)) return;
        $types = '';
        foreach ($params as $param) {
            $types .= ($param === null) ? 'i' : 's';
        }
        $stmt->bind_param($types, ...$params);
    }
    
    public function escape($string) {
        return $this->db->real_escape_string($string);
    }
}