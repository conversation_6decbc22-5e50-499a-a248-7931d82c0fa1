<?php
/**
 * Debug Script
 * Check session and authentication status
 */

// Start session
session_start();

// Set Indian timezone
date_default_timezone_set('Asia/Kolkata');

// Include configuration
require_once 'config/config.php';

// Include core classes
require_once 'application/core/Auth.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Debug - Auth Status</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head><body class='bg-light p-4'>";

echo "<div class='container'>";
echo "<h2>Authentication Debug</h2>";

echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>Session Information</h5></div>";
echo "<div class='card-body'>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Session Status:</strong> " . session_status() . "</p>";
echo "<p><strong>Session Data:</strong></p>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";
echo "</div></div>";

echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>Authentication Status</h5></div>";
echo "<div class='card-body'>";

try {
    echo "<p><strong>Is Logged In:</strong> " . (Auth::isLoggedIn() ? 'YES' : 'NO') . "</p>";
    echo "<p><strong>User ID:</strong> " . (Auth::getUserId() ?? 'None') . "</p>";
    echo "<p><strong>Username:</strong> " . (Auth::getUsername() ?? 'None') . "</p>";
    echo "<p><strong>User Role:</strong> " . (Auth::getUserRole() ?? 'None') . "</p>";
    echo "<p><strong>Is Admin:</strong> " . (Auth::isAdmin() ? 'YES' : 'NO') . "</p>";
    echo "<p><strong>Is Staff:</strong> " . (Auth::isStaff() ? 'YES' : 'NO') . "</p>";
} catch (Exception $e) {
    echo "<p class='text-danger'>Error checking auth status: " . $e->getMessage() . "</p>";
}

echo "</div></div>";

echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>Test Actions</h5></div>";
echo "<div class='card-body'>";

// Test login
if (isset($_POST['test_login'])) {
    $result = Auth::attempt('admin', 'admin123');
    if ($result['success']) {
        echo "<div class='alert alert-success'>Login successful! Refresh page to see updated status.</div>";
    } else {
        echo "<div class='alert alert-danger'>Login failed: " . $result['message'] . "</div>";
    }
}

// Test logout
if (isset($_POST['test_logout'])) {
    Auth::logout();
    echo "<div class='alert alert-info'>Logged out! Refresh page to see updated status.</div>";
}

echo "<form method='POST' class='d-inline'>";
echo "<button type='submit' name='test_login' class='btn btn-primary me-2'>Test Login (admin/admin123)</button>";
echo "</form>";

echo "<form method='POST' class='d-inline'>";
echo "<button type='submit' name='test_logout' class='btn btn-secondary me-2'>Test Logout</button>";
echo "</form>";

echo "<a href='simple_test.php' class='btn btn-info me-2'>Simple Test</a>";
echo "<a href='index.php' class='btn btn-success'>Go to App</a>";

echo "</div></div>";

echo "<div class='card'>";
echo "<div class='card-header'><h5>Environment Info</h5></div>";
echo "<div class='card-body'>";
echo "<p><strong>Environment:</strong> " . ENVIRONMENT . "</p>";
echo "<p><strong>Base URL:</strong> " . BASE_URL . "</p>";
echo "<p><strong>Current URL:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>";
echo "<p><strong>HTTP Host:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . "</p>";
echo "<p><strong>Server Name:</strong> " . ($_SERVER['SERVER_NAME'] ?? 'Unknown') . "</p>";
echo "</div></div>";

echo "</div>";
echo "</body></html>";
?>
