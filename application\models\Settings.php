<?php
/**
 * Settings Model
 * Handles application settings
 */

class Settings extends Model {
    protected $table = 'settings';
    private static $cache = [];
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * Get setting value by key
     */
    public function get($key, $default = null) {
        // Check cache first
        if (isset(self::$cache[$key])) {
            return self::$cache[$key];
        }
        
        $sql = "SELECT setting_value, setting_type FROM settings WHERE setting_key = ?";
        $result = $this->query($sql, [$key]);
        
        if ($result && $row = $result->fetch_assoc()) {
            $value = $this->castValue($row['setting_value'], $row['setting_type']);
            self::$cache[$key] = $value;
            return $value;
        }
        
        return $default;
    }
    
    /**
     * Set setting value
     */
    public function set($key, $value, $type = 'string', $description = '') {
        $stringValue = $this->valueToString($value, $type);
        
        $sql = "INSERT INTO settings (setting_key, setting_value, setting_type, description) 
                VALUES (?, ?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                setting_type = VALUES(setting_type),
                description = VALUES(description)";
        
        $result = $this->query($sql, [$key, $stringValue, $type, $description]);
        
        if ($result) {
            // Update cache
            self::$cache[$key] = $value;
            return true;
        }
        
        return false;
    }
    
    /**
     * Get all settings
     */
    public function getAll() {
        $sql = "SELECT * FROM settings ORDER BY setting_key ASC";
        $result = $this->query($sql);
        
        if ($result) {
            $settings = [];
            while ($row = $result->fetch_assoc()) {
                $settings[$row['setting_key']] = [
                    'value' => $this->castValue($row['setting_value'], $row['setting_type']),
                    'type' => $row['setting_type'],
                    'description' => $row['description'],
                    'updated_at' => $row['updated_at']
                ];
            }
            return $settings;
        }
        
        return [];
    }
    
    /**
     * Update multiple settings
     */
    public function updateMultiple($settings) {
        $success = true;
        
        foreach ($settings as $key => $data) {
            $value = $data['value'] ?? $data;
            $type = $data['type'] ?? 'string';
            $description = $data['description'] ?? '';
            
            if (!$this->set($key, $value, $type, $description)) {
                $success = false;
            }
        }
        
        return $success;
    }
    
    /**
     * Delete setting
     */
    public function delete($key) {
        $sql = "DELETE FROM settings WHERE setting_key = ?";
        $result = $this->query($sql, [$key]);
        
        if ($result) {
            unset(self::$cache[$key]);
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if setting exists
     */
    public function exists($key) {
        $sql = "SELECT COUNT(*) as count FROM settings WHERE setting_key = ?";
        $result = $this->query($sql, [$key]);
        return $result ? $result->fetch_assoc()['count'] > 0 : false;
    }
    
    /**
     * Cast value based on type
     */
    private function castValue($value, $type) {
        switch ($type) {
            case 'boolean':
                return (bool) $value;
            case 'number':
                return is_numeric($value) ? (float) $value : 0;
            case 'json':
                return json_decode($value, true) ?: [];
            default:
                return $value;
        }
    }
    
    /**
     * Convert value to string for storage
     */
    private function valueToString($value, $type) {
        switch ($type) {
            case 'boolean':
                return $value ? '1' : '0';
            case 'number':
                return (string) $value;
            case 'json':
                return json_encode($value);
            default:
                return (string) $value;
        }
    }
    
    /**
     * Get company settings
     */
    public function getCompanySettings() {
        $keys = [
            'company_name',
            'company_address',
            'company_phone',
            'company_email',
            'company_gstin'
        ];
        
        $settings = [];
        foreach ($keys as $key) {
            $settings[$key] = $this->get($key, '');
        }
        
        return $settings;
    }
    
    /**
     * Get invoice settings
     */
    public function getInvoiceSettings() {
        return [
            'default_tax_rate' => $this->get('default_tax_rate', TAX_RATE),
            'currency_symbol' => $this->get('currency_symbol', CURRENCY_SYMBOL),
            'invoice_terms' => $this->get('invoice_terms', 'Payment due within 30 days'),
            'invoice_prefix' => $this->get('invoice_prefix', INVOICE_PREFIX)
        ];
    }
    
    /**
     * Get notification settings
     */
    public function getNotificationSettings() {
        return [
            'low_stock_alert' => $this->get('low_stock_alert', true),
            'email_notifications' => $this->get('email_notifications', true),
            'sms_notifications' => $this->get('sms_notifications', false)
        ];
    }
    
    /**
     * Clear settings cache
     */
    public function clearCache() {
        self::$cache = [];
    }
    
    /**
     * Export settings
     */
    public function export() {
        $settings = $this->getAll();
        return json_encode($settings, JSON_PRETTY_PRINT);
    }
    
    /**
     * Import settings
     */
    public function import($jsonData) {
        $settings = json_decode($jsonData, true);
        
        if (!$settings) {
            return false;
        }
        
        return $this->updateMultiple($settings);
    }
    
    /**
     * Reset to default settings
     */
    public function resetToDefaults() {
        $defaultSettings = [
            'company_name' => ['value' => 'Your Company Name', 'type' => 'string'],
            'company_address' => ['value' => 'Your Company Address', 'type' => 'string'],
            'company_phone' => ['value' => '+91-9999999999', 'type' => 'string'],
            'company_email' => ['value' => '<EMAIL>', 'type' => 'string'],
            'company_gstin' => ['value' => 'GSTIN123456789', 'type' => 'string'],
            'default_tax_rate' => ['value' => 18, 'type' => 'number'],
            'currency_symbol' => ['value' => '₹', 'type' => 'string'],
            'invoice_terms' => ['value' => 'Payment due within 30 days', 'type' => 'string'],
            'low_stock_alert' => ['value' => true, 'type' => 'boolean'],
            'email_notifications' => ['value' => true, 'type' => 'boolean']
        ];
        
        return $this->updateMultiple($defaultSettings);
    }
}
