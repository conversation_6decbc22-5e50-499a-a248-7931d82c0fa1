<?php
/**
 * Base Controller Class
 * All controllers extend from this class
 */

class Controller {
    protected $view;
    protected $cache;
    
    public function __construct() {
        $this->view = new View();
        $this->cache = new Cache();
        
        // Check session timeout
        $this->checkSessionTimeout();
    }
    
    /**
     * Check if session has timed out
     */
    private function checkSessionTimeout() {
        if (isset($_SESSION['last_activity'])) {
            if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
                session_destroy();
                redirectTo('login?timeout=1');
            }
        }
        $_SESSION['last_activity'] = time();
    }
    
    /**
     * Render view with layout
     */
    protected function render($view, $data = [], $layout = 'main') {
        // Add common data to all views
        $data['app_name'] = APP_NAME;
        $data['base_url'] = BASE_URL;
        $data['current_user'] = $this->getCurrentUser();
        $data['is_admin'] = isAdmin();
        $data['is_staff'] = isStaff();
        
        $this->view->render($view, $data, $layout);
    }
    
    /**
     * Render JSON response
     */
    protected function renderJson($data, $status = 200) {
        header('Content-Type: application/json');
        http_response_code($status);
        echo json_encode($data);
        exit();
    }
    
    /**
     * Get current logged in user
     */
    protected function getCurrentUser() {
        if (!isLoggedIn()) {
            return null;
        }
        
        $cacheKey = 'user_' . $_SESSION['user_id'];
        $user = $this->cache->get($cacheKey);
        
        if (!$user) {
            $userModel = new User();
            $user = $userModel->findById($_SESSION['user_id']);
            $this->cache->set($cacheKey, $user, 300); // Cache for 5 minutes
        }
        
        return $user;
    }
    
    /**
     * Validate CSRF token
     */
    protected function validateCsrfToken() {
        $token = $_POST['csrf_token'] ?? '';
        if (!hash_equals($_SESSION['csrf_token'] ?? '', $token)) {
            $this->renderJson(['error' => 'Invalid CSRF token'], 403);
        }
    }
    
    /**
     * Generate CSRF token
     */
    protected function generateCsrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Validate input data
     */
    protected function validate($data, $rules) {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? '';
            $ruleArray = explode('|', $rule);
            
            foreach ($ruleArray as $singleRule) {
                if ($singleRule === 'required' && empty($value)) {
                    $errors[$field] = ucfirst($field) . ' is required';
                    break;
                }
                
                if (strpos($singleRule, 'min:') === 0 && strlen($value) < substr($singleRule, 4)) {
                    $errors[$field] = ucfirst($field) . ' must be at least ' . substr($singleRule, 4) . ' characters';
                    break;
                }
                
                if (strpos($singleRule, 'max:') === 0 && strlen($value) > substr($singleRule, 4)) {
                    $errors[$field] = ucfirst($field) . ' must not exceed ' . substr($singleRule, 4) . ' characters';
                    break;
                }
                
                if ($singleRule === 'email' && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $errors[$field] = ucfirst($field) . ' must be a valid email address';
                    break;
                }
                
                if ($singleRule === 'numeric' && !is_numeric($value)) {
                    $errors[$field] = ucfirst($field) . ' must be a number';
                    break;
                }
                
                if ($singleRule === 'positive' && $value <= 0) {
                    $errors[$field] = ucfirst($field) . ' must be a positive number';
                    break;
                }
            }
        }
        
        return $errors;
    }
    
    /**
     * Set flash message
     */
    protected function setFlash($type, $message) {
        $_SESSION['flash'][$type] = $message;
    }
    
    /**
     * Get flash messages
     */
    protected function getFlash() {
        $flash = $_SESSION['flash'] ?? [];
        unset($_SESSION['flash']);
        return $flash;
    }
    
    /**
     * Upload file
     */
    protected function uploadFile($file, $directory = 'uploads/') {
        if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
            return ['success' => false, 'error' => 'No file uploaded'];
        }
        
        // Check file size
        if ($file['size'] > UPLOAD_MAX_SIZE) {
            return ['success' => false, 'error' => 'File size exceeds maximum allowed size'];
        }
        
        // Check file type
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, ALLOWED_FILE_TYPES)) {
            return ['success' => false, 'error' => 'File type not allowed'];
        }
        
        // Generate unique filename
        $filename = uniqid() . '_' . time() . '.' . $extension;
        $filepath = $directory . $filename;
        
        // Create directory if it doesn't exist
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            return ['success' => true, 'filename' => $filename, 'filepath' => $filepath];
        } else {
            return ['success' => false, 'error' => 'Failed to upload file'];
        }
    }
    
    /**
     * Paginate results
     */
    protected function paginate($totalRecords, $currentPage = 1, $recordsPerPage = RECORDS_PER_PAGE) {
        $totalPages = ceil($totalRecords / $recordsPerPage);
        $currentPage = max(1, min($totalPages, $currentPage));
        $offset = ($currentPage - 1) * $recordsPerPage;
        
        return [
            'total_records' => $totalRecords,
            'total_pages' => $totalPages,
            'current_page' => $currentPage,
            'records_per_page' => $recordsPerPage,
            'offset' => $offset,
            'has_previous' => $currentPage > 1,
            'has_next' => $currentPage < $totalPages,
            'previous_page' => $currentPage - 1,
            'next_page' => $currentPage + 1
        ];
    }
    
    /**
     * Log user activity
     */
    protected function logActivity($action, $details = '') {
        logActivity($action, $details);
    }
    
    /**
     * Check if request is AJAX
     */
    protected function isAjax() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Redirect with message
     */
    protected function redirectWithMessage($url, $type, $message) {
        $this->setFlash($type, $message);
        redirectTo($url);
    }
}
