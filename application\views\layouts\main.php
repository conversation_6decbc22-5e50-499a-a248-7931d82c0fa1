<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? APP_NAME ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?= $this->asset('css/style.css') ?>" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <meta name="csrf-token" content="<?= $csrf_token ?>">
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <h3><?= APP_NAME ?></h3>
                <button type="button" id="sidebarCollapse" class="btn btn-outline-light btn-sm">
                    <i class="bi bi-list"></i>
                </button>
            </div>
            
            <ul class="list-unstyled components">
                <li class="<?= $this->isActive('dashboard') ? 'active' : '' ?>">
                    <a href="<?= $this->url('?page=dashboard') ?>">
                        <i class="bi bi-speedometer2"></i>
                        Dashboard
                    </a>
                </li>
                
                <li class="<?= strpos($current_url, 'invoices') !== false ? 'active' : '' ?>">
                    <a href="#invoiceSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="bi bi-receipt"></i>
                        Invoices
                    </a>
                    <ul class="collapse list-unstyled" id="invoiceSubmenu">
                        <li><a href="<?= $this->url('?page=invoices') ?>">All Invoices</a></li>
                        <li><a href="<?= $this->url('?page=invoices/create') ?>">Create Invoice</a></li>
                        <li><a href="<?= $this->url('?page=invoices&status=draft') ?>">Draft Invoices</a></li>
                        <li><a href="<?= $this->url('?page=invoices&status=overdue') ?>">Overdue Invoices</a></li>
                    </ul>
                </li>
                
                <li class="<?= strpos($current_url, 'stock') !== false ? 'active' : '' ?>">
                    <a href="#stockSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="bi bi-box-seam"></i>
                        Stock Management
                    </a>
                    <ul class="collapse list-unstyled" id="stockSubmenu">
                        <li><a href="<?= $this->url('?page=stock') ?>">All Products</a></li>
                        <li><a href="<?= $this->url('?page=stock/create') ?>">Add Product</a></li>
                        <li><a href="<?= $this->url('?page=stock/low-stock') ?>">Low Stock Alert</a></li>
                    </ul>
                </li>
                
                <li class="<?= strpos($current_url, 'customers') !== false ? 'active' : '' ?>">
                    <a href="#customerSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="bi bi-people"></i>
                        Customers
                    </a>
                    <ul class="collapse list-unstyled" id="customerSubmenu">
                        <li><a href="<?= $this->url('?page=customers') ?>">All Customers</a></li>
                        <li><a href="<?= $this->url('?page=customers/create') ?>">Add Customer</a></li>
                    </ul>
                </li>
                
                <li class="<?= strpos($current_url, 'reports') !== false ? 'active' : '' ?>">
                    <a href="#reportSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="bi bi-graph-up"></i>
                        Reports
                    </a>
                    <ul class="collapse list-unstyled" id="reportSubmenu">
                        <li><a href="<?= $this->url('?page=reports') ?>">Overview</a></li>
                        <li><a href="<?= $this->url('?page=reports/sales') ?>">Sales Report</a></li>
                        <li><a href="<?= $this->url('?page=reports/stock') ?>">Stock Report</a></li>
                    </ul>
                </li>
                
                <?php if ($is_admin): ?>
                <li class="<?= strpos($current_url, 'users') !== false ? 'active' : '' ?>">
                    <a href="#userSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="bi bi-person-gear"></i>
                        User Management
                    </a>
                    <ul class="collapse list-unstyled" id="userSubmenu">
                        <li><a href="<?= $this->url('?page=users') ?>">All Users</a></li>
                        <li><a href="<?= $this->url('?page=users/create') ?>">Add User</a></li>
                    </ul>
                </li>
                
                <li class="<?= $this->isActive('settings') ? 'active' : '' ?>">
                    <a href="<?= $this->url('?page=settings') ?>">
                        <i class="bi bi-gear"></i>
                        Settings
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        
        <!-- Page Content -->
        <div id="content" class="content">
            <!-- Top Navigation -->
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapseTop" class="btn btn-outline-secondary d-lg-none">
                        <i class="bi bi-list"></i>
                    </button>
                    
                    <div class="navbar-nav ms-auto">
                        <!-- Notifications -->
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="notificationDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-bell"></i>
                                <span class="badge bg-danger" id="notificationCount">0</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" id="notificationList">
                                <li><h6 class="dropdown-header">Notifications</h6></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-muted" href="#">No new notifications</a></li>
                            </ul>
                        </div>
                        
                        <!-- User Menu -->
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle"></i>
                                <?= $current_user['username'] ?? 'User' ?>
                                <span class="badge bg-<?= $is_admin ? 'danger' : 'primary' ?> ms-1">
                                    <?= $is_admin ? 'Admin' : 'Staff' ?>
                                </span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><h6 class="dropdown-header">Welcome, <?= $current_user['first_name'] ?? $current_user['username'] ?>!</h6></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= $this->url('?page=profile') ?>">
                                    <i class="bi bi-person"></i> Profile
                                </a></li>
                                <li><a class="dropdown-item" href="<?= $this->url('?page=settings') ?>">
                                    <i class="bi bi-gear"></i> Settings
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= $this->url('?page=logout') ?>">
                                    <i class="bi bi-box-arrow-right"></i> Logout
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>
            
            <!-- Flash Messages -->
            <?php if (!empty($flash)): ?>
                <div class="container-fluid mt-3">
                    <?php foreach ($flash as $type => $message): ?>
                        <div class="alert alert-<?= $type === 'error' ? 'danger' : $type ?> alert-dismissible fade show" role="alert">
                            <?= $this->escape($message) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <!-- Main Content -->
            <div class="container-fluid mt-3">
                <?= $content ?>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?= $this->asset('js/app.js') ?>"></script>
    
    <!-- Page specific scripts -->
    <?php if (isset($scripts)): ?>
        <?php foreach ($scripts as $script): ?>
            <script src="<?= $this->asset('js/' . $script) ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
</body>
</html>
