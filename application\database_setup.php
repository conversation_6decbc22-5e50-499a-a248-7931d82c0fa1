<?php
/**
 * Database Setup and Migration
 * Creates all necessary tables for the Invoice and Stock Management System
 */

try {
    // Create database if it doesn't exist
    $tempDb = new mysqli(DB_HOST, DB_USER, DB_PASS);
    if ($tempDb->connect_errno) {
        die("Failed to connect to MySQL: " . $tempDb->connect_error);
    }

    $tempDb->query("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $tempDb->close();

    // Connect to the database
    $db = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($db->connect_errno) {
        die("Failed to connect to MySQL: " . $db->connect_error);
    }

    // Set charset
    $db->set_charset("utf8mb4");

    // Create users table
    $sql = "
    CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        role ENUM('admin', 'staff') NOT NULL DEFAULT 'staff',
        first_name VARCHAR(50),
        last_name VARCHAR(50),
        phone VARCHAR(20),
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_role (role)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $db->query($sql);

    // Create customers table
    $sql = "
    CREATE TABLE IF NOT EXISTS customers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100),
        phone VARCHAR(20),
        address TEXT,
        city VARCHAR(50),
        state VARCHAR(50),
        pincode VARCHAR(10),
        gstin VARCHAR(20),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_name (name),
        INDEX idx_email (email),
        INDEX idx_phone (phone)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $db->query($sql);

    // Create categories table
    $sql = "
    CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_name (name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $db->query($sql);

    // Create products table
    $sql = "
    CREATE TABLE IF NOT EXISTS products (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        category_id INT,
        sku VARCHAR(50) UNIQUE,
        barcode VARCHAR(50),
        unit VARCHAR(20) DEFAULT 'pcs',
        purchase_price DECIMAL(10, 2) DEFAULT 0.00,
        selling_price DECIMAL(10, 2) NOT NULL,
        tax_rate DECIMAL(5, 2) DEFAULT 18.00,
        min_stock_level INT DEFAULT 10,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
        INDEX idx_name (name),
        INDEX idx_sku (sku),
        INDEX idx_barcode (barcode),
        INDEX idx_category (category_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $db->query($sql);

    // Create stock table
    $sql = "
    CREATE TABLE IF NOT EXISTS stock (
        id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT NOT NULL,
        quantity INT NOT NULL DEFAULT 0,
        reserved_quantity INT DEFAULT 0,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        UNIQUE KEY unique_product (product_id),
        INDEX idx_quantity (quantity)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $db->query($sql);

    // Create stock_movements table
    $sql = "
    CREATE TABLE IF NOT EXISTS stock_movements (
        id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT NOT NULL,
        movement_type ENUM('in', 'out', 'adjustment') NOT NULL,
        quantity INT NOT NULL,
        reference_type ENUM('purchase', 'sale', 'adjustment', 'return') NOT NULL,
        reference_id INT,
        notes TEXT,
        user_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_product (product_id),
        INDEX idx_type (movement_type),
        INDEX idx_reference (reference_type, reference_id),
        INDEX idx_created (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $db->query($sql);

    // Create invoices table
    $sql = "
    CREATE TABLE IF NOT EXISTS invoices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        invoice_number VARCHAR(20) UNIQUE NOT NULL,
        customer_id INT NOT NULL,
        user_id INT NOT NULL,
        invoice_date DATE NOT NULL,
        due_date DATE NOT NULL,
        subtotal DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
        tax_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
        discount_amount DECIMAL(12, 2) DEFAULT 0.00,
        total_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
        paid_amount DECIMAL(12, 2) DEFAULT 0.00,
        status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') NOT NULL DEFAULT 'draft',
        notes TEXT,
        terms_conditions TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
        INDEX idx_invoice_number (invoice_number),
        INDEX idx_customer (customer_id),
        INDEX idx_user (user_id),
        INDEX idx_date (invoice_date),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $db->query($sql);

    // Create invoice_items table
    $sql = "
    CREATE TABLE IF NOT EXISTS invoice_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        invoice_id INT NOT NULL,
        product_id INT NOT NULL,
        product_name VARCHAR(100) NOT NULL,
        quantity DECIMAL(10, 2) NOT NULL,
        unit_price DECIMAL(10, 2) NOT NULL,
        tax_rate DECIMAL(5, 2) DEFAULT 18.00,
        line_total DECIMAL(12, 2) NOT NULL,
        FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT,
        INDEX idx_invoice (invoice_id),
        INDEX idx_product (product_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $db->query($sql);

    // Create payments table
    $sql = "
    CREATE TABLE IF NOT EXISTS payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        invoice_id INT NOT NULL,
        payment_date DATE NOT NULL,
        amount DECIMAL(12, 2) NOT NULL,
        payment_method ENUM('cash', 'card', 'bank_transfer', 'cheque', 'upi', 'other') NOT NULL,
        reference_number VARCHAR(100),
        notes TEXT,
        user_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_invoice (invoice_id),
        INDEX idx_date (payment_date),
        INDEX idx_method (payment_method)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $db->query($sql);

    // Create settings table
    $sql = "
    CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
        description TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_key (setting_key)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $db->query($sql);

    // Create activity_logs table
    $sql = "
    CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        action VARCHAR(100) NOT NULL,
        table_name VARCHAR(50),
        record_id INT,
        old_values JSON,
        new_values JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_user (user_id),
        INDEX idx_action (action),
        INDEX idx_table (table_name),
        INDEX idx_created (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $db->query($sql);

    // Insert default admin user if not exists
    $adminCheck = $db->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
    $adminExists = $adminCheck->fetch_assoc()['count'] > 0;

    if (!$adminExists) {
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, email, password_hash, role, first_name, last_name)
                VALUES ('admin', '<EMAIL>', ?, 'admin', 'System', 'Administrator')";
        $stmt = $db->prepare($sql);
        $stmt->bind_param('s', $adminPassword);
        $stmt->execute();
    }

    // Insert default staff user if not exists
    $staffCheck = $db->query("SELECT COUNT(*) as count FROM users WHERE username = 'staff'");
    $staffExists = $staffCheck->fetch_assoc()['count'] > 0;

    if (!$staffExists) {
        $staffPassword = password_hash('staff123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, email, password_hash, role, first_name, last_name)
                VALUES ('staff', '<EMAIL>', ?, 'staff', 'Demo', 'Staff')";
        $stmt = $db->prepare($sql);
        $stmt->bind_param('s', $staffPassword);
        $stmt->execute();
    }

    // Insert default categories
    $categoryCheck = $db->query("SELECT COUNT(*) as count FROM categories");
    $categoriesExist = $categoryCheck->fetch_assoc()['count'] > 0;

    if (!$categoriesExist) {
        $categories = [
            ['General', 'General products'],
            ['Electronics', 'Electronic items and gadgets'],
            ['Clothing', 'Apparel and accessories'],
            ['Food & Beverages', 'Food items and drinks'],
            ['Books', 'Books and publications'],
            ['Sports', 'Sports equipment and accessories']
        ];

        $stmt = $db->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
        foreach ($categories as $category) {
            $stmt->bind_param('ss', $category[0], $category[1]);
            $stmt->execute();
        }
    }

    // Insert default settings
    $settingsCheck = $db->query("SELECT COUNT(*) as count FROM settings");
    $settingsExist = $settingsCheck->fetch_assoc()['count'] > 0;

    if (!$settingsExist) {
        $defaultSettings = [
            ['company_name', 'Your Company Name', 'string', 'Company name for invoices'],
            ['company_address', 'Your Company Address', 'string', 'Company address for invoices'],
            ['company_phone', '+91-9999999999', 'string', 'Company phone number'],
            ['company_email', '<EMAIL>', 'string', 'Company email address'],
            ['company_gstin', 'GSTIN123456789', 'string', 'Company GSTIN number'],
            ['default_tax_rate', '18', 'number', 'Default tax rate percentage'],
            ['currency_symbol', '₹', 'string', 'Currency symbol'],
            ['invoice_terms', 'Payment due within 30 days', 'string', 'Default invoice terms'],
            ['low_stock_alert', '1', 'boolean', 'Enable low stock alerts'],
            ['email_notifications', '1', 'boolean', 'Enable email notifications']
        ];

        $stmt = $db->prepare("INSERT INTO settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)");
        foreach ($defaultSettings as $setting) {
            $stmt->bind_param('ssss', $setting[0], $setting[1], $setting[2], $setting[3]);
            $stmt->execute();
        }
    }

    echo "Database setup completed successfully!";

} catch (Exception $e) {
    die("Database setup failed: " . $e->getMessage());
}