<?php
/**
 * Clear Session Script
 * Clears all session data and cookies
 */

// Start session
session_start();

// Clear all session data
$_SESSION = array();

// Delete session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy session
session_destroy();

// Clear any application cookies
$cookies = ['remember_token', 'user_preferences', 'sidebar_state'];
foreach ($cookies as $cookie) {
    if (isset($_COOKIE[$cookie])) {
        setcookie($cookie, '', time() - 3600, '/');
    }
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Session Cleared</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head><body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-6'>";
echo "<div class='card'>";
echo "<div class='card-body text-center'>";
echo "<h3 class='text-success'>✓ Session Cleared</h3>";
echo "<p>All session data and cookies have been cleared.</p>";
echo "<div class='mt-4'>";
echo "<a href='debug.php' class='btn btn-info me-2'>Check Debug</a>";
echo "<a href='index.php' class='btn btn-primary'>Go to App</a>";
echo "</div>";
echo "</div></div></div></div></div>";

echo "</body></html>";
?>
