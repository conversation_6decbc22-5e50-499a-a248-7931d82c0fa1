<?php
/**
 * Login Controller
 * Handles user authentication
 */

class LoginController {
    private $view;

    public function __construct() {
        $this->view = new View();
    }

    /**
     * Display login form
     */
    public function display() {
        $data = [
            'title' => 'Login - ' . APP_NAME,
            'csrf_token' => $this->generateCsrfToken(),
            'timeout' => isset($_GET['timeout']) ? 'Your session has expired. Please login again.' : null,
            'error' => $_SESSION['login_error'] ?? null
        ];

        // Clear login error after displaying
        unset($_SESSION['login_error']);

        $this->view->render('auth/login', $data, 'auth');
    }

    /**
     * Generate CSRF token
     */
    private function generateCsrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}