<?php
class LoginController extends Controller {
    public function __construct(User $user) {
        $this->user = $user;
    }

    public function display() {
        $view = new View();
        $view->render('login');
    }

    public function attempt() {
        if (isset($_POST['username'], $_POST['password'])) {
            $username = $_POST['username'];
            $password = $_POST['password'];

            if ($this->user->attemptLogin($username, $password)) {
                redirect(BASE_URL . "dashboard");
                exit();
            } else {
                $view = new View();
                $view->setData(['error' => 'Invalid username or password']);
                $view->render('login');
            }
        }
    }
}