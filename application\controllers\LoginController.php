<?php
/**
 * Login Controller
 * Handles user authentication
 */

class LoginController extends Controller {

    public function __construct() {
        // Don't call parent constructor to avoid session timeout check
        $this->view = new View();
        $this->cache = new Cache();
    }

    /**
     * Display login form
     */
    public function display() {
        // Redirect if already logged in
        if (Auth::isLoggedIn()) {
            redirectTo('dashboard');
        }

        $data = [
            'title' => 'Login - ' . APP_NAME,
            'csrf_token' => $this->generateCsrfToken(),
            'timeout' => isset($_GET['timeout']) ? 'Your session has expired. Please login again.' : null
        ];

        $this->render('auth/login', $data, 'auth');
    }

    /**
     * Attempt login
     */
    public function attempt() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirectTo('login');
        }

        // Validate CSRF token
        $this->validateCsrfToken();

        $username = sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';

        // Validate input
        $errors = $this->validate([
            'username' => $username,
            'password' => $password
        ], [
            'username' => 'required',
            'password' => 'required'
        ]);

        if (!empty($errors)) {
            $this->renderLoginWithErrors($errors);
            return;
        }

        // Attempt authentication
        $result = Auth::attempt($username, $password);

        if ($result['success']) {
            // Update last login time
            $userModel = new User();
            $userModel->updateLastLogin(Auth::getUserId());

            // Log successful login
            $this->logActivity('Login', "User logged in successfully");

            // Redirect to dashboard
            redirectTo('dashboard');
        } else {
            $this->renderLoginWithErrors(['login' => $result['message']]);
        }
    }

    /**
     * Logout user
     */
    public function logout() {
        Auth::logout();
        redirectTo('login');
    }

    /**
     * Render login form with errors
     */
    private function renderLoginWithErrors($errors) {
        $data = [
            'title' => 'Login - ' . APP_NAME,
            'csrf_token' => $this->generateCsrfToken(),
            'errors' => $errors,
            'old_username' => $_POST['username'] ?? ''
        ];

        $this->render('auth/login', $data, 'auth');
    }
}