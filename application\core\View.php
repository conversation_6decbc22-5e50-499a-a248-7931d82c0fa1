<?php
/**
 * Enhanced View Class
 * Handles view rendering with layouts and data management
 */

class View {
    private $data = [];
    private $layoutData = [];

    /**
     * Set data for view
     */
    public function setData($data) {
        $this->data = array_merge($this->data, $data);
    }

    /**
     * Set layout data
     */
    public function setLayoutData($data) {
        $this->layoutData = array_merge($this->layoutData, $data);
    }

    /**
     * Render view with optional layout
     */
    public function render($view, $data = [], $layout = null) {
        // Merge data
        $this->setData($data);

        // Add common data
        $this->data['csrf_token'] = $_SESSION['csrf_token'] ?? '';
        $this->data['flash'] = $this->getFlashMessages();
        $this->data['current_url'] = $_SERVER['REQUEST_URI'] ?? '';

        if ($layout) {
            $this->renderWithLayout($view, $layout);
        } else {
            $this->renderView($view);
        }
    }

    /**
     * Render view with layout
     */
    private function renderWithLayout($view, $layout) {
        // Capture view content
        ob_start();
        $this->renderView($view);
        $content = ob_get_clean();

        // Set content for layout
        $this->setLayoutData(['content' => $content]);

        // Render layout
        $this->renderLayout($layout);
    }

    /**
     * Render view file
     */
    private function renderView($view) {
        $viewFile = VIEWS_PATH . $view . '.php';

        if (!file_exists($viewFile)) {
            throw new Exception("View file not found: $viewFile");
        }

        // Extract data to variables
        extract($this->data);

        // Include view file
        require $viewFile;
    }

    /**
     * Render layout file
     */
    private function renderLayout($layout) {
        $layoutFile = VIEWS_PATH . 'layouts/' . $layout . '.php';

        if (!file_exists($layoutFile)) {
            throw new Exception("Layout file not found: $layoutFile");
        }

        // Merge layout data with view data
        $data = array_merge($this->data, $this->layoutData);
        extract($data);

        // Include layout file
        require $layoutFile;
    }

    /**
     * Get flash messages
     */
    private function getFlashMessages() {
        $flash = $_SESSION['flash'] ?? [];
        unset($_SESSION['flash']);
        return $flash;
    }

    /**
     * Escape HTML output
     */
    public function escape($string) {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Include partial view
     */
    public function partial($partial, $data = []) {
        $partialFile = VIEWS_PATH . 'partials/' . $partial . '.php';

        if (!file_exists($partialFile)) {
            throw new Exception("Partial file not found: $partialFile");
        }

        // Merge data
        $partialData = array_merge($this->data, $data);
        extract($partialData);

        // Include partial file
        require $partialFile;
    }

    /**
     * Generate URL
     */
    public function url($path = '') {
        return BASE_URL . ltrim($path, '/');
    }

    /**
     * Generate asset URL
     */
    public function asset($path) {
        return BASE_URL . ASSETS_PATH . ltrim($path, '/');
    }

    /**
     * Check if current page matches route
     */
    public function isActive($route) {
        $currentRoute = $_GET['page'] ?? '';
        return $currentRoute === $route;
    }

    /**
     * Format currency
     */
    public function currency($amount) {
        return formatCurrency($amount);
    }

    /**
     * Format date
     */
    public function date($date, $format = DATE_FORMAT) {
        return formatDate($date, $format);
    }

    /**
     * Format datetime
     */
    public function datetime($datetime, $format = DATETIME_FORMAT) {
        return formatDateTime($datetime, $format);
    }
}